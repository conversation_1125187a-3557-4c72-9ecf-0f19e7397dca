# Set List Generator

> Our platform provides musicians with a single, streamlined solution for creating, managing, and
> printing clear, auto-formatted setlists—eliminating
> messy documents, reducing last-minute confusion, and
> ensuring every band member has accurate information
> for every performance.

## Features

- **Smart PDF Generation**

  - Automatic font size optimization for readability
  - Dynamic spacing calculation for optimal page layout
  - One set per page with clear headers
  - Musical notation support (♭/♯) with proper vertical alignment
  - All text in uppercase for better visibility on stage
  - Encore section support with visual separator

- **Setlist Management**

  - Create and manage multiple show setlists
  - Organize songs into up to 4 sets
  - Track song keys with proper musical notation (♭/♯)
  - Intuitive drag-and-drop song reordering
  - Quick set-to-set song movement with arrow controls
  - Duplicate song detection
  - Encore section management
  - Real-time duration tracking

- **User Interface**
  - Modern, responsive design for all devices
  - Real-time PDF preview with slide-out panel
  - Automatic local storage persistence
  - Cloud storage support with sync indicator
  - Efficient keyboard shortcuts
  - Contextual hover controls
  - Three button sizes: default, compact, and tiny
  - Ghost button variants for subtle actions

## Tech Stack

- Vue 3 with Composition API
- TypeScript
- jsPDF for PDF generation
- date-fns for date formatting
- vuedraggable for drag-and-drop
- Vite for development and building
- Firebase for cloud storage (optional)
- Lucide icons

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- pnpm (required)

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/set-list-generator.git
cd set-list-generator

# Install dependencies
pnpm install

# Start development server
pnpm dev
```

## Usage

### Creating a Setlist

1. Click "New Setlist" to start
2. Enter venue, act name, and date
3. Add songs to each set with titles and keys
4. Use drag and drop to reorder songs
5. Use arrow buttons to quickly move songs between sets
6. Preview the PDF using the "Show PDF" button
7. Mark songs as encore items by dragging them past the encore separator
8. Save your setlist locally or to the cloud

### PDF Generation Features

- Headers show venue, act, date, and set number
- Font sizes automatically adjust for optimal readability
- Musical accidentals (♭/♯) properly aligned
- Songs are evenly spaced on the page
- Each set prints on a new page
- Encore sections clearly marked with separator line
- All text in uppercase for stage visibility

### Keyboard Shortcuts

- Enter: Add new song
- Ctrl/Cmd + Enter: Add new set
- Delete/Backspace: Remove song (when empty)
- Esc: Close PDF preview

## Development

### Project Structure

```
src/
├── components/          # Vue components
│   ├── PDFSetList.vue  # PDF preview component
│   ├── SetInput.vue    # Set management
│   ├── base/           # Base components
│   │   └── Button.vue  # Configurable button component
│   └── ...
├── composables/        # Shared composition logic
├── types/              # TypeScript definitions
├── utils/              # Utility functions
│   └── pdfGenerator.ts # PDF generation logic
└── styles/             # CSS and variables
```

### Key Components

- `PDFSetList.vue`: Handles PDF preview with slide-out panel
- `SetInput.vue`: Manages song input and ordering with set-to-set movement
- `Button.vue`: Flexible button component with size and variant options
- `pdfGenerator.ts`: Core PDF generation with JSDoc documentation

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Author

Dave Collison
