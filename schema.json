{"collections": {"users": {"fields": {"email": {"type": "string", "required": true}, "uid": {"type": "string", "required": true}, "displayName": {"type": "string", "required": true}, "createdAt": {"type": "timestamp", "required": true}, "updatedAt": {"type": "timestamp", "required": true}}}, "shows": {"fields": {"title": {"type": "string", "required": true}, "venue": {"type": "string", "required": true}, "act": {"type": "string", "required": true}, "date": {"type": "timestamp", "required": true}, "setListId": {"type": "reference", "collection": "setLists", "required": true}, "createdBy": {"type": "string", "collection": "users", "required": true}, "createdAt": {"type": "timestamp", "required": true}, "updatedAt": {"type": "timestamp", "required": true}}}, "setLists": {"fields": {"name": {"type": "string", "required": true}, "sets": {"type": "array", "maxLength": 4, "items": {"setId": {"type": "reference", "collection": "sets", "required": true}, "order": {"type": "number", "min": 1, "max": 4, "required": true}}}, "createdBy": {"type": "string", "collection": "users", "required": true}, "createdAt": {"type": "timestamp", "required": true}, "updatedAt": {"type": "timestamp", "required": true}, "lastUsed": {"type": "timestamp", "required": true}}}, "sets": {"fields": {"name": {"type": "string", "required": true}, "songs": {"type": "array", "items": {"songId": {"type": "reference", "collection": "songs", "required": true}, "order": {"type": "number", "required": true}, "isLastSong": {"type": "boolean", "default": false}}}, "createdBy": {"type": "string", "collection": "users", "required": true}, "createdAt": {"type": "timestamp", "required": true}, "updatedAt": {"type": "timestamp", "required": true}}}, "songs": {"fields": {"title": {"type": "string", "required": true}, "key": {"type": "string", "required": true}, "artist": {"type": "string"}, "duration": {"type": "number", "description": "Duration in seconds"}, "tempo": {"type": "number", "description": "BPM"}, "notes": {"type": "string"}, "lastPerformed": {"type": "timestamp"}, "timesPerformed": {"type": "number", "default": 0}, "createdBy": {"type": "string", "collection": "users", "required": true}, "createdAt": {"type": "timestamp", "required": true}, "updatedAt": {"type": "timestamp", "required": true}}}}, "version": "1.0.0", "description": "Firestore Database Structure for Setlist Creator", "rules": {"setLists": {"maxSets": 4}, "sets": {"minSongs": 1}}}