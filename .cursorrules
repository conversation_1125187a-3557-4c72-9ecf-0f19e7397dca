This project is a web application for creating, organising and managing setlists for printing and sharing with bandmates.

"A single place to create, organise and print error-free setlists so musicians can focus on performing, not paperwork."

Do not destroy any existing code.
Use the existing code as a guide to write new code.
Use good abstraction.
Use latest documentation. Ask for links to documentation if you need it.

The package manager for the project is PNPM.

when creating new single file components, place the script at the top of the file.

When referring to an event, it shall be called a show and not a gig or a booking.

## Styling

Use the latest CSS technologies for responsive design and accessibility, such as flexbox, grid, container queries, logical properties, custom properties and media queries.
Try to keep the styles in one main file wherever possible, only using styles in SFCs where necessary.

Keep the README.md up to date as much as possible. Keep it clean and remove any references to old code.

## Testing

Use Vitest for testing, with snapshots for UI components and unit tests for backend logic.
Keep test up-to-date with code.

## Documentation

Keep the README.md up to date as much as possible. Keep it clean and remove any references to old code.
Use JSDoc for documentation and add it as code is added.
