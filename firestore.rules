rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isSignedIn() {
      return request.auth != null;
    }
    
    function isOwner(data) {
      return isSignedIn() && data.createdBy == request.auth.uid;
    }

    function isValidTimestamp(ts) {
      return ts is timestamp && ts <= request.time;
    }

    function hasRequiredFields(data, fields) {
      return data.keys().hasAll(fields);
    }

    // Users Collection
    match /users/{userId} {
      allow read: if isSignedIn();
      allow create: if isSignedIn() 
        && request.auth.uid == userId
        && hasRequiredFields(request.resource.data, ['email', 'uid', 'displayName', 'createdAt', 'updatedAt'])
        && request.resource.data.uid == userId;
      allow update: if isSignedIn() 
        && request.auth.uid == userId;
      allow delete: if false; // Users cannot be deleted through client
    }

    // Shows Collection
    match /shows/{showId} {
      function hasValidShowFields() {
        let data = request.resource.data;
        return hasRequiredFields(data, ['title', 'venue', 'act', 'date', 'createdBy', 'createdAt', 'updatedAt'])
          && data.title is string
          && data.venue is string
          && data.act is string
          && isValidTimestamp(data.date);
      }

      allow read: if isSignedIn();
      allow create: if isSignedIn() 
        && request.resource.data.createdBy == request.auth.uid
        && hasValidShowFields();
      allow update: if isOwner(resource.data) 
        && hasValidShowFields();
      allow delete: if isOwner(resource.data);
    }

    // SetLists Collection
    match /setLists/{setListId} {
      function hasValidSetListFields() {
        let data = request.resource.data;
        return hasRequiredFields(data, ['name', 'sets', 'createdBy', 'createdAt', 'updatedAt'])
          && data.name is string
          && data.sets is list;
      }

      allow read: if isSignedIn();
      allow create: if isSignedIn() 
        && request.resource.data.createdBy == request.auth.uid
        && hasValidSetListFields();
      allow update: if isOwner(resource.data) 
        && hasValidSetListFields();
      allow delete: if isOwner(resource.data);
    }

    // Sets Collection
    match /sets/{setId} {
      function hasValidSetFields() {
        let data = request.resource.data;
        return hasRequiredFields(data, ['name', 'songs', 'createdBy', 'createdAt', 'updatedAt'])
          && data.name is string
          && data.songs is list;
      }

      allow read: if isSignedIn();
      allow create: if isSignedIn() 
        && request.resource.data.createdBy == request.auth.uid
        && hasValidSetFields();
      allow update: if isOwner(resource.data) 
        && hasValidSetFields();
      allow delete: if isOwner(resource.data);
    }

    // Songs Collection
    match /songs/{songId} {
      function hasValidSongFields() {
        let data = request.resource.data;
        return hasRequiredFields(data, ['title', 'key', 'createdBy', 'createdAt', 'updatedAt'])
          && data.title is string
          && data.key is string
          && (!('duration' in data) || data.duration is number)
          && (!('tempo' in data) || data.tempo is number)
          && (!('timesPerformed' in data) || data.timesPerformed is number)
          && (!('lastPerformed' in data) || isValidTimestamp(data.lastPerformed));
      }

      allow read: if isSignedIn();
      allow create: if isSignedIn() 
        && request.resource.data.createdBy == request.auth.uid
        && hasValidSongFields();
      allow update: if isOwner(resource.data) 
        && hasValidSongFields();
      allow delete: if isOwner(resource.data);
    }
  }
}