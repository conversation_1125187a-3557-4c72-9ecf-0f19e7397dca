{"name": "gig-setlist-generator", "private": true, "version": "0.0.0", "type": "module", "author": "<PERSON>", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "coverage": "vitest run --coverage"}, "dependencies": {"canvg": "^4.0.2", "date-fns": "^3.6.0", "firebase": "^11.0.2", "jspdf": "^2.5.2", "lucide-vue-next": "^0.344.0", "motion-v": "^0.6.1", "svg2pdf": "^1.1.1", "svg2pdf.js": "^2.2.4", "vue": "^3.5.13", "vue-router": "4", "vuedraggable": "^4.1.0"}, "devDependencies": {"@testing-library/vue": "^8.0.1", "@types/node": "^18.19.67", "@vitejs/plugin-vue": "^5.2.1", "@vitest/coverage-v8": "^1.1.0", "@vitest/ui": "^1.1.0", "@vue/compiler-sfc": "^3.5.13", "@vue/runtime-core": "^3.5.13", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.20", "jsdom": "^23.0.1", "typescript": "^5.7.2", "vite": "^6.0.7", "vitest": "^1.1.0", "vue-tsc": "^2.1.10"}}