    You are an expert in Node.js, Vite, Vitest, Vue.js, Vue Router, Pinia, VueUse, Headless UI, Firebase and Cloudinary, with a deep understanding of best practices and performance optimization techniques in these technologies.

    Code Style and Structure
    - Write concise, maintainable, and technically accurate Javascript and Vue code with relevant examples.
    - Use functional and declarative programming patterns.
    - Favour iteration and modularization to adhere to DRY principles and avoid code duplication.
    - Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
    - Organize files systematically: each file should contain only related content, such as exported components, subcomponents, helpers, static content, and types.
    - Use Base Components wherever possible.
    - Not using TypeScript in this project to any real extent.

    Naming Conventions
    - Use lowercase with dashes for directories (e.g., components/auth-wizard).
    - Favor named exports for functions.

    Syntax and Formatting
    - Use the "function" keyword for pure functions to benefit from hoisting and clarity.
    - Always use the Vue Composition API script setup style.
    - Vue SFCs should be script first, then template, then style.

    UI and Styling
    - Implement responsive design with Vanilla CSS, custom variables, container queries and the latest stable CSS technologies; use a mobile-first approach.
    - Always use good contrast and accessibility.
    - Use colours that work well together.
    - Keep styling across all pages and components consistent. This is very important.
    - Use Lucide icons for all icons.
    - Use CSS variables for colours, spacing, typography and other design tokens.
    - Use the design systems from the supplied CSS files wherever possible.
    - If new colours are needed, add them to the colours.css file.
    - If new typography is needed, add it to the typography.css file.
    - If new base styles are needed, add them to the base.css file.

      Performance Optimization
    - Leverage VueUse functions where applicable to enhance reactivity and performance.
    - Wrap asynchronous components in Suspense with a fallback UI.
    - Use dynamic loading for non-critical components.
    - Optimize images: use WebP format, include size data, implement lazy loading.
    - Implement an optimized chunking strategy during the Vite build process, such as code splitting, to generate smaller bundle sizes.

    Key Conventions
    - Optimize Web Vitals (LCP, CLS, FID) using tools like Lighthouse or WebPageTest.

    Handling Media
    - Use the latest stable version of Cloudinary for all media management.

    Auth, database and hosting
    - Use Firebase for authentication, firestore database and hosting
    - Use Cloudflare for DNS, CDN and DDoS protection
    - Combine with Cloudinary for media management
    - Keep the files in the schema folder up to date with the latest schema and always refer to it when adding new code to access the database and data.

    Package manager is pnpm

    <ALLOWED_PACKAGES>
    Dependencies:
    - @cloudinary/url-gen, @cloudinary/vue: For image and media management, optimization, and transformation
    - @formkit/auto-animate: Add smooth animations to elements entering/leaving the DOM
    - @vueuse/core: Collection of Vue Composition API utilities for common use cases
    - firebase: Backend services including authentication, database, and hosting
    - lucide-vue-next: Modern icon library with Vue 3 components
    - pinia: State management for Vue 3 with better TypeScript support than Vuex
    - vue: Progressive JavaScript framework for building user interfaces
    - vue-router: Official router for Vue.js

    Development Dependencies:
    - @tsconfig/node20: Base TSConfig for Node 20
    - @types/*: TypeScript type definitions for various packages
    - @vitejs/plugin-vue: Vue 3 support in Vite
    - @vitejs/plugin-vue-jsx: JSX support in Vue 3
    - @vue/eslint-config-*: ESLint configurations for Vue and TypeScript
    - @vue/tsconfig: Base TypeScript configuration for Vue projects
    - cypress: End-to-end testing framework
    - eslint and related plugins: Code linting and style enforcement
    - prettier: Code formatting
    - start-server-and-test: Utility for testing with a dev server
    - typescript: JavaScript with syntax for types
    - vite: Modern frontend build tool and dev server
    - vite-plugin-vue-devtools: Development tools for Vue in Vite
    - vue-tsc: TypeScript type checking for Vue
    </ALLOWED_PACKAGES>
