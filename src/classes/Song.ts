import { watch } from 'vue'
import { db } from '@/firebase'
import { 
  doc, 
  updateDoc, 
  deleteDoc, 
  Timestamp,
  serverTimestamp 
} from 'firebase/firestore'

export class Song {
  id: string
  title: string
  shortTitle?: string
  key: string
  artist?: string
  durationSecs?: number
  bpm?: number
  notes?: string
  year?: number
  composers?: string[]
  lastPerformed?: Date
  timesPerformed: number
  createdBy: string
  createdAt: Date
  updatedAt: Date

  constructor(songDetails: Partial<Song> = {}) {
    this.id = songDetails.id || ''
    this.title = songDetails.title || ''
    this.shortTitle = songDetails.shortTitle || ''
    this.key = songDetails.key || ''
    this.artist = songDetails.artist
    this.durationSecs = songDetails.durationSecs
    this.bpm = songDetails.bpm
    this.notes = songDetails.notes
    this.year = songDetails.year
    this.composers = songDetails.composers || []
    this.lastPerformed = songDetails.lastPerformed
    this.timesPerformed = songDetails.timesPerformed || 0
    this.createdBy = songDetails.createdBy || ''
    this.createdAt = songDetails.createdAt || new Date()
    this.updatedAt = songDetails.updatedAt || new Date()
  }

  async update(): Promise<void> {
    const songRef = doc(db, 'songs', this.id)
    await updateDoc(songRef, this.toFirestore())
  }

  async delete(): Promise<void> {
    const songRef = doc(db, 'songs', this.id)
    await deleteDoc(songRef)
  }

  watchChanges(): void {
    watch(
      () => this,
      async () => {
        await this.update()
      },
      { deep: true }
    )
  }

  toFirestore() {
    const data: any = {
      title: this.title,
      shortTitle: this.shortTitle,
      key: this.key,
      createdBy: this.createdBy,
      createdAt: Timestamp.fromDate(this.createdAt),
      updatedAt: serverTimestamp(),
      timesPerformed: this.timesPerformed
    }

    // Only add optional fields if they have values
    if (this.artist) data.artist = this.artist
    if (this.durationSecs) data.durationSecs = this.durationSecs
    if (this.bpm) data.bpm = this.bpm
    if (this.notes) data.notes = this.notes
    if (typeof this.year === 'number') data.year = this.year
    if (this.composers?.length) data.composers = this.composers
    if (this.lastPerformed) data.lastPerformed = Timestamp.fromDate(this.lastPerformed)

    return data
  }

  static fromFirestore(data: any, id: string): Song {
    return new Song({
      id,
      ...data,
      lastPerformed: data.lastPerformed?.toDate(),
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date()
    })
  }

  duration(): string {
    if (!this.durationSecs) return ''
    const minutes = Math.floor(this.durationSecs / 60)
    const seconds = this.durationSecs % 60
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`
  }

  // Helper method to convert comma-separated string to composers array
  setComposers(composersString: string) {
    this.composers = composersString
      .split(',')
      .map(composer => composer.trim())
      .filter(composer => composer !== '')
  }

  // Helper method to convert composers array to comma-separated string
  getComposersString(): string {
    return this.composers?.join(', ') || ''
  }
}