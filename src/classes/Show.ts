import { Set, StorageLocation } from '@/types'
import { watch } from 'vue'
import { db } from '@/firebase'
import { doc, updateDoc } from 'firebase/firestore'

export class Show {
  id: string
  title: string
  venue: string
  act: string
  date: string
  storageLocation: StorageLocation
  sets: Set[]
  notes?: string

  constructor(showDetails: Partial<Show> = {}) {
    this.id = showDetails.id || ''
    this.title = showDetails.title || ''
    this.venue = showDetails.venue || ''
    this.act = showDetails.act || ''
    this.date = showDetails.date || ''
    this.storageLocation = showDetails.storageLocation || 'local'
    this.sets = showDetails.sets || []
    this.notes = showDetails.notes
  }

  addSet(set: Set): void {
    this.sets.push(set)
  }

  removeSet(setId: string): void {
    this.sets = this.sets.filter(set => set.id !== setId)
  }

  watchChanges(): void {
    watch(
      () => this,
      async (currentValue, oldValue) => {
        if (this.storageLocation === 'cloud') {
          // sync to firestore
          const showRef = doc(db, 'shows', this.id)
          await updateDoc(showRef, {
            title: this.title,
            venue: this.venue,
            act: this.act,
            date: this.date,
            sets: this.sets,
            notes: this.notes
          })
          console.log('syncing to firestore', currentValue, oldValue)
        }
      },
      {
        deep: true
      }
    )
  }
}