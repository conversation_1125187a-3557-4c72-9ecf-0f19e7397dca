import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import DurationDisplay from '../DurationDisplay.vue'

describe('DurationDisplay.vue', () => {
  it('formats duration in minutes correctly', () => {
    const wrapper = mount(DurationDisplay, {
      props: {
        seconds: 3900 // 65 minutes
      }
    })
    expect(wrapper.text()).toContain('65:00')
  })

  it('handles zero duration', () => {
    const wrapper = mount(DurationDisplay, {
      props: {
        seconds: 0
      }
    })
    expect(wrapper.text()).toContain('0:00')
  })

  it('formats single-digit minutes correctly', () => {
    const wrapper = mount(DurationDisplay, {
      props: {
        seconds: 300 // 5 minutes
      }
    })
    expect(wrapper.text()).toContain('5:00')
  })

  it('formats seconds correctly', () => {
    const wrapper = mount(DurationDisplay, {
      props: {
        seconds: 65 // 1 minute and 5 seconds
      }
    })
    expect(wrapper.text()).toContain('1:05')
  })

  it('applies warning class when warning prop is true', () => {
    const wrapper = mount(DurationDisplay, {
      props: {
        seconds: 300,
        warning: true
      }
    })
    expect(wrapper.classes()).toContain('duration-warning')
  })

  it('applies error class when error prop is true', () => {
    const wrapper = mount(DurationDisplay, {
      props: {
        seconds: 300,
        error: true
      }
    })
    expect(wrapper.classes()).toContain('duration-error')
  })

  it('shows duration in brackets when brackets prop is true', () => {
    const wrapper = mount(DurationDisplay, {
      props: {
        seconds: 300,
        brackets: true
      }
    })
    expect(wrapper.text()).toContain('(5:00)')
  })
})
