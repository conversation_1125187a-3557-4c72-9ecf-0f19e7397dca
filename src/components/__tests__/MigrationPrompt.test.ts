import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import MigrationPrompt from '../MigrationPrompt.vue'
import { useShowStorage } from '@/composables/useShowStorage'

// Mock the useShowStorage composable
vi.mock('@/composables/useShowStorage', () => ({
  useShowStorage: () => ({
    shows: {
      value: [
        {
          id: '1',
          venue: 'Test Venue 1',
          act: 'Test Act 1',
          date: '2024-12-19T21:04:05Z',
          storageLocation: 'local',
          sets: []
        },
        {
          id: '2',
          venue: 'Test Venue 2',
          act: 'Test Act 2',
          date: '2024-12-19T21:04:05Z',
          storageLocation: 'local',
          sets: []
        },
        {
          id: '3',
          venue: 'Test Venue 3',
          act: 'Test Act 3',
          date: '2024-12-19T21:04:05Z',
          storageLocation: 'cloud',
          sets: []
        }
      ]
    }
  })
}))

describe('MigrationPrompt.vue', () => {
  const mountComponent = () => {
    return mount(MigrationPrompt, {
      props: {
        onClose: () => {}
      },
      global: {
        stubs: {
          HardDrive: true,
          Cloud: true,
          Check: true,
          X: true,
          BaseButton: {
            template: '<button><slot/></button>',
            props: ['variant', 'disabled']
          }
        }
      }
    })
  }

  it('renders only local shows', () => {
    const wrapper = mountComponent()
    const showItems = wrapper.findAll('.show-item')
    expect(showItems).toHaveLength(2) // Only local shows should be displayed
  })

  it('allows selecting and deselecting individual shows', async () => {
    const wrapper = mountComponent()
    const showItems = wrapper.findAll('.show-item')
    
    // Select first show
    await showItems[0].trigger('click')
    expect(wrapper.find('.show-item.selected').exists()).toBe(true)
    expect(wrapper.findAll('.show-item.selected')).toHaveLength(1)
    
    // Deselect first show
    await showItems[0].trigger('click')
    expect(wrapper.find('.show-item.selected').exists()).toBe(false)
  })

  it('handles select all and deselect all actions', async () => {
    const wrapper = mountComponent()
    
    // Select all shows
    await wrapper.find('button.select-all').trigger('click')
    expect(wrapper.findAll('.show-item.selected')).toHaveLength(2)
    
    // Deselect all shows
    await wrapper.find('button.deselect-all').trigger('click')
    expect(wrapper.findAll('.show-item.selected')).toHaveLength(0)
  })

  it('emits migrate event with selected show IDs', async () => {
    const wrapper = mountComponent()
    const showItems = wrapper.findAll('.show-item')
    
    // Select both shows
    await showItems[0].trigger('click')
    await showItems[1].trigger('click')
    
    // Click migrate button
    const migrateButton = wrapper.find('button:first-child')
    await migrateButton.trigger('click')
    
    expect(wrapper.emitted('migrate')).toBeTruthy()
    expect(wrapper.emitted('migrate')![0][0]).toEqual(['1', '2'])
    expect(wrapper.emitted('close')).toBeTruthy()
  })

  it('disables migrate button when no shows are selected', () => {
    const wrapper = mountComponent()
    const migrateButton = wrapper.find('button:first-child')
    expect(migrateButton.attributes('disabled')).toBeDefined()
  })

  it('enables migrate button when shows are selected', async () => {
    const wrapper = mountComponent()
    const showItem = wrapper.find('.show-item')
    
    await showItem.trigger('click')
    const migrateButton = wrapper.find('button:first-child')
    expect(migrateButton.attributes('disabled')).toBeUndefined()
  })

  it('displays correct show information', () => {
    const wrapper = mountComponent()
    const firstShow = wrapper.find('.show-item')
    
    expect(firstShow.find('h3').text()).toBe('Test Venue 1')
    expect(firstShow.find('p').text()).toContain('Test Act 1')
  })

  it('emits close event when clicking overlay or close button', async () => {
    const wrapper = mountComponent()
    
    await wrapper.find('.modal-overlay').trigger('click')
    expect(wrapper.emitted('close')).toBeTruthy()
    
    await wrapper.find('.close-button').trigger('click')
    expect(wrapper.emitted('close')).toHaveLength(2)
  })

  // Snapshot test for the component's structure
  it('matches snapshot', () => {
    const wrapper = mountComponent()
    expect(wrapper.html()).toMatchSnapshot()
  })
})
