import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import SongInput from '../SongInput.vue'

describe('SongInput.vue', () => {
  it('renders input with correct props', () => {
    const wrapper = mount(SongInput, {
      props: {
        id: 'test-song',
        modelValue: '',
        placeholder: 'Song Title',
        class: 'custom-class'
      }
    })
    const input = wrapper.find('input')
    expect(input.exists()).toBe(true)
    expect(input.attributes('id')).toBe('test-song')
    expect(input.attributes('placeholder')).toBe('Song Title')
    expect(input.classes()).toContain('custom-class')
  })

  it('displays the modelValue correctly', () => {
    const wrapper = mount(SongInput, {
      props: {
        modelValue: 'Sweet Home Alabama'
      }
    })
    expect(wrapper.find('input').element.value).toBe('Sweet Home Alabama')
  })

  it('emits update:modelValue event with input value', async () => {
    const wrapper = mount(SongInput, {
      props: {
        modelValue: ''
      }
    })
    const input = wrapper.find('input')
    
    await input.setValue('Sweet Home Alabama')
    expect(wrapper.emitted('update:modelValue')?.[0]).toEqual(['Sweet Home Alabama'])
  })

  it('converts key input to uppercase when placeholder is "Key"', async () => {
    const wrapper = mount(SongInput, {
      props: {
        modelValue: '',
        placeholder: 'Key'
      }
    })
    const input = wrapper.find('input')
    
    await input.setValue('am')
    expect(wrapper.emitted('update:modelValue')?.[0]).toEqual(['AM'])
  })

  it('emits input and keydown events', async () => {
    const wrapper = mount(SongInput, {
      props: {
        modelValue: ''
      }
    })
    const input = wrapper.find('input')
    
    await input.trigger('input')
    expect(wrapper.emitted('input')).toBeTruthy()

    await input.trigger('keydown')
    expect(wrapper.emitted('keydown')).toBeTruthy()
  })

  it('preserves case for regular song titles', async () => {
    const wrapper = mount(SongInput, {
      props: {
        modelValue: '',
        placeholder: 'Song Title'
      }
    })
    const input = wrapper.find('input')
    
    await input.setValue('Sweet Home Alabama')
    expect(wrapper.emitted('update:modelValue')?.[0]).toEqual(['Sweet Home Alabama'])
  })
})
