import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import KeyInput from '../KeyInput.vue'

describe('KeyInput.vue', () => {
  it('renders input with correct props', () => {
    const wrapper = mount(KeyInput, {
      props: {
        id: 'test-key',
        modelValue: '',
        placeholder: 'Key',
        class: 'custom-class'
      }
    })
    const input = wrapper.find('input')
    expect(input.exists()).toBe(true)
    expect(input.attributes('id')).toBe('test-key')
    expect(input.attributes('placeholder')).toBe('Key')
    expect(input.classes()).toContain('custom-class')
  })

  it('formats major keys correctly', async () => {
    const wrapper = mount(KeyInput, {
      props: {
        modelValue: 'c'
      }
    })
    expect(wrapper.find('input').element.value).toBe('C')

    // Test with flat
    await wrapper.setProps({ modelValue: 'B' })
    expect(wrapper.find('input').element.value).toBe('B')

    // Test with sharp
    await wrapper.setProps({ modelValue: 'F' })
    expect(wrapper.find('input').element.value).toBe('F')
  })

  it('formats minor keys correctly', async () => {
    const wrapper = mount(KeyInput, {
      props: {
        modelValue: 'am'
      }
    })
    expect(wrapper.find('input').element.value).toBe('Am')

    // Test with flat
    await wrapper.setProps({ modelValue: 'bbm' })
    expect(wrapper.find('input').element.value).toBe('B♭m')

    // Test with sharp
    await wrapper.setProps({ modelValue: 'f#m' })
    expect(wrapper.find('input').element.value).toBe('F♯m')
  })

  it('emits update:modelValue event with formatted value', async () => {
    const wrapper = mount(KeyInput, {
      props: {
        modelValue: ''
      }
    })
    const input = wrapper.find('input')
    
    // Test input with 'b' for flat
    await input.setValue('Bb')
    const updateEvents = wrapper.emitted('update:modelValue')
    expect(updateEvents?.[0]?.[0]).toBe('B♭')

    // Test input with sharp and minor
    await input.setValue('f#m')
    expect(updateEvents?.[1]?.[0]).toBe('F♯m')
  })

  it('emits input and keydown events', async () => {
    const wrapper = mount(KeyInput, {
      props: {
        modelValue: ''
      }
    })
    const input = wrapper.find('input')
    
    await input.trigger('input')
    expect(wrapper.emitted('input')).toBeTruthy()

    await input.trigger('keydown')
    expect(wrapper.emitted('keydown')).toBeTruthy()
  })

  it('handles invalid input gracefully', async () => {
    const wrapper = mount(KeyInput, {
      props: {
        modelValue: 'H' // Invalid note
      }
    })
    expect(wrapper.find('input').element.value).toBe('H')
  })
})
