// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`MigrationPrompt.vue > matches snapshot 1`] = `
"<div data-v-a36861ee="" class="migration-prompt">
  <div data-v-a36861ee="" class="modal-overlay"></div>
  <div data-v-a36861ee="" class="modal-content">
    <div data-v-a36861ee="" class="modal-header">
      <h2 data-v-a36861ee="">Migrate Local Shows</h2><button data-v-a36861ee="" class="close-button"><svg data-v-a36861ee="" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" size="20" class="lucide lucide-xicon">
          <path d="M18 6 6 18"></path>
          <path d="m6 6 12 12"></path>
        </svg></button>
    </div>
    <p data-v-a36861ee="" class="description"> You have 2 shows stored locally. Would you like to migrate them to the cloud? </p>
    <div data-v-a36861ee="" class="actions"><button data-v-a36861ee="" class="select-all">Select All</button><button data-v-a36861ee="" class="deselect-all">Deselect All</button></div>
    <div data-v-a36861ee="" class="shows-list">
      <div data-v-a36861ee="" class="show-item">
        <div data-v-a36861ee="" class="show-info">
          <h3 data-v-a36861ee="">Test Venue 1</h3>
          <p data-v-a36861ee="">Test Act 1 - 12/19/2024</p>
        </div>
        <div data-v-a36861ee="" class="storage-indicator"><svg data-v-a36861ee="" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" size="16" class="lucide lucide-hard-drive-icon">
            <line x1="22" x2="2" y1="12" y2="12"></line>
            <path d="M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path>
            <line x1="6" x2="6.01" y1="16" y2="16"></line>
            <line x1="10" x2="10.01" y1="16" y2="16"></line>
          </svg></div>
        <div data-v-a36861ee="" class="checkbox">
          <!--v-if-->
        </div>
      </div>
      <div data-v-a36861ee="" class="show-item">
        <div data-v-a36861ee="" class="show-info">
          <h3 data-v-a36861ee="">Test Venue 2</h3>
          <p data-v-a36861ee="">Test Act 2 - 12/19/2024</p>
        </div>
        <div data-v-a36861ee="" class="storage-indicator"><svg data-v-a36861ee="" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" size="16" class="lucide lucide-hard-drive-icon">
            <line x1="22" x2="2" y1="12" y2="12"></line>
            <path d="M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path>
            <line x1="6" x2="6.01" y1="16" y2="16"></line>
            <line x1="10" x2="10.01" y1="16" y2="16"></line>
          </svg></div>
        <div data-v-a36861ee="" class="checkbox">
          <!--v-if-->
        </div>
      </div>
    </div>
    <div data-v-a36861ee="" class="modal-footer"><button data-v-a36861ee=""> Migrate Selected Shows </button><button data-v-a36861ee=""> Skip </button></div>
  </div>
</div>"
`;
