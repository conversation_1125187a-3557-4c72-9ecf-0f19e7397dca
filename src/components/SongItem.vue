<script setup lang="ts">
import { useRouter } from 'vue-router'
import type { Song } from '@/classes/Song'

const router = useRouter()
const props = defineProps<{
  song: Song
}>()

const handleClick = () => {
  router.push({ name: 'song-details', params: { id: props.song.id } })
}

const handleEdit = (e: Event) => {
  e.stopPropagation() // Prevent navigation when clicking edit
  // Go to edit page
  router.push({ name: 'edit-song', params: { id: props.song.id }, query: { edit: 'true' } })
}

const handleDelete = async (e: Event) => {
  e.stopPropagation() // Prevent navigation when clicking delete
  await props.song.delete()
}
</script>

<template>
  <div class="song-item" role="button" @click="handleClick">
    <div class="song-info">
      <span class="title">{{ song.title }}</span>
      <span v-if="song.title !== song.shortTitle && song.shortTitle" class="short-title">({{ song.shortTitle }})</span>
      <span class="key">{{ song.key }}</span>
      <span v-if="song.artist" class="artist">{{ song.artist }}</span>
    </div>
    <div class="song-actions">
      <BaseButton @click="handleEdit" size="compact" variant="secondary">Edit</BaseButton>
      <BaseButton @click="handleDelete" size="compact" variant="danger">Delete</BaseButton>
    </div>
  </div>
</template>

<style scoped>
.song-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-bottom: 1px solid var(--color-border);
  cursor: pointer;
  transition: background-color 0.2s;
}

.song-item:hover {
  background: var(--color-surface-hover);
}

.song-info {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.title {
  font-weight: 500;
}

.short-title {
  color: var(--color-text-muted);
  font-size: 0.9em;
}

.key {
  font-size: 0.9em;
  padding: var(--space-2xs) var(--space-xs);
  background: var(--color-surface-accent);
  border-radius: var(--radius-sm);
}

.artist {
  color: var(--color-text-muted);
  font-size: 0.9em;
}

.song-actions {
  display: flex;
  gap: var(--space-xs);
  opacity: 0;
  transition: opacity 0.2s;
}

.song-item:hover .song-actions {
  opacity: 1;
}
</style>