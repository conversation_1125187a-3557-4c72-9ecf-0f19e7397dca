<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const props = defineProps<{
  trigger: any
}>()

const isOpen = ref(false)
const dropdownRef = ref<HTMLElement | null>(null)

const closeDropdown = (e: MouseEvent) => {
  if (dropdownRef.value && !dropdownRef.value.contains(e.target as Node)) {
    isOpen.value = false
  }
}

const handleClick = (e: MouseEvent) => {
  const target = e.target as HTMLElement
  if (target.closest('a') || target.closest('button')) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', closeDropdown)
})

onUnmounted(() => {
  document.removeEventListener('click', closeDropdown)
})
</script>

<template>
  <div class="dropdown" ref="dropdownRef">
    <div class="dropdown-trigger" @click="isOpen = !isOpen">
      <component :is="trigger" />
    </div>
    <div v-if="isOpen" class="dropdown-content" @click="handleClick">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  position: absolute;
  top: calc(100% + var(--space-xs));
  right: 0;
  min-width: 200px;
  background: var(--color-surface);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  padding: var(--space-xs);
  z-index: 1000;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: black;
  color: white;
  padding: var(--space-xs);
  aspect-ratio: 1;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: opacity var(--transition-fast);

  &:hover {
    opacity: 0.8;
  }
}
</style>
