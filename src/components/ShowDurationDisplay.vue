<script setup lang="ts">
import { computed } from 'vue'
import { Song } from '@/models/Song'
import { calculateShowDuration } from '@/utils/duration'

interface Props {
  sets: Song[][]
  showLabel?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showLabel: false
})

const duration = computed(() => calculateShowDuration(props.sets))

const formatDuration = computed(() => {
  const totalMinutes = Math.floor(duration.value.totalSeconds / 60)
  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60
  const seconds = duration.value.totalSeconds % 60

  // Format for display (hours:minutes)
  const displayText = hours > 0
    ? `${hours}h ${minutes}m`
    : `${minutes}m`

  // Format for tooltip (minutes:seconds)
  const tooltipText = `${totalMinutes}m ${seconds}s`

  return { displayText, tooltipText }
})
</script>

<template>
  <div class="show-duration">
    <span v-if="showLabel" class="duration-label">Total Show Duration:</span>
    <span class="duration" :class="{ warning: duration.warning, error: duration.error }"
      :title="formatDuration.tooltipText">
      {{ formatDuration.displayText }}
    </span>
  </div>
</template>

<style scoped>
.show-duration {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
}

.duration-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-light);
}

.duration {
  cursor: help;
}

.duration.warning {
  color: var(--color-warning);
}

.duration.error {
  color: var(--color-error);
}
</style>