<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
import type { Show, StoredShow } from '@/types/'
import { generateSetListPDF } from '@/utils/pdfGenerator'

const props = defineProps<{
  gigData: Show | StoredShow
}>()

const emit = defineEmits(['close'])

const previewUrl = ref<string>('')
const uppercase = ref(true)

const updatePreview = async () => {
  try {
    const doc = await generateSetListPDF(props.gigData, uppercase.value)
    previewUrl.value = doc.output('datauristring')
  } catch (error) {
    console.error('Error generating PDF preview:', error)
    // Optionally show an error message to the user
  }
}

watch(() => props.gigData, updatePreview, {
  deep: true,
  immediate: true
})

// Function to handle keypress for 'esc'
const handleKeyPress = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    emit('close')
  }
}

// Function to handle click outside the panel
const handleClickOutside = (event: MouseEvent) => {
  const panel = document.querySelector('.pdf-panel')
  // Ignore clicks on preview buttons
  const isPreviewButton = (event.target as Element)?.closest('.action-button.preview')
  if (panel && !panel.contains(event.target as Node) && !isPreviewButton) {
    emit('close')
  }
}

const changeCase = () => {
  uppercase.value = !uppercase.value
  updatePreview()
}

onMounted(() => {
  document.addEventListener('keydown', handleKeyPress)
  document.addEventListener('click', handleClickOutside, {
    capture: true,
  })
})

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeyPress)
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div class="pdf-panel">
    <div class="pdf-header">
      <h2>PDF Preview</h2>
      <BaseButton size="compact" :variant="uppercase ? 'primary' : 'ghost'" @click="changeCase">Aa</BaseButton>
      <BaseButton size="compact" variant="ghost" @click="$emit('close')" class="close-button">×</BaseButton>
    </div>
    <div class="pdf-content">
      <iframe :src="previewUrl" class="pdf-preview"></iframe>
    </div>
  </div>
</template>

<style>
.pdf-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 600px;
  height: 100vh;
  background: var(--color-surface);
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  animation: slideIn var(--transition-normal) ease-out;
}

.pdf-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md);
  border-bottom: 1px solid var(--color-text-light);
}

.pdf-header h2 {
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.close-button {
  font-size: var(--font-size-xl);
  color: var(--color-text-light);
  padding: var(--space-2xs) var(--space-xs);
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast);
}

.close-button:hover {
  color: var(--color-text);
}

.pdf-content {
  flex: 1;
  padding: var(--space-md);
  overflow: auto;
}

.pdf-preview {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: var(--radius-md);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translateX(0);
  }
}
</style>