<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  id?: string
  modelValue: string
  placeholder?: string
  class?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'input', event: Event): void
  (e: 'keydown', event: KeyboardEvent): void
}>()

// Musical key formatting
const formattedKey = computed(() => {
  if (!props.modelValue) return ''

  // Split the input into parts (note, accidental, and quality)
  const matches = props.modelValue.match(/^([A-Ga-g])([b#])?([mM])?$/)
  if (!matches) return props.modelValue

  const [_, note, accidental, quality] = matches

  // Format each part
  const formattedNote = note.toUpperCase()
  const formattedAccidental = accidental === 'b' ? '♭' : accidental === '#' ? '♯' : accidental
  const formattedQuality = quality?.toLowerCase() || ''

  return formattedNote + (formattedAccidental || '') + formattedQuality
})

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  let value = target.value

  // Normalize the input
  value = value.replace(/^([A-Ga-g])([b#])?([mM])?$/i, (_, note, accidental, quality) => {
    const normalizedNote = note.toUpperCase()
    const normalizedAccidental = accidental ? (accidental.toLowerCase() === 'b' ? '♭' : '♯') : ''
    const normalizedQuality = quality?.toLowerCase() || ''
    return normalizedNote + normalizedAccidental + normalizedQuality
  })

  emit('update:modelValue', value)
  emit('input', event)
}

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event)
}
</script>

<template>
  <input :id="id" type="text" :value="formattedKey" @input="handleInput" @keydown="handleKeydown"
    :placeholder="placeholder" class="key-input" :class maxlength="7" title="Musical Key (e.g., C, Am, E♭, F#m)" />
</template>

<style>
.key-input {
  background: none;
  border: 1px solid transparent;
  border-radius: var(--radius-sm);
  transition: border-color var(--transition-fast);
  box-shadow: var(--shadow-sm);
  text-align: center;
  font-family: system-ui, -apple-system, sans-serif;
}

.key-input:focus {
  border-color: var(--color-primary-light);
  outline: none;
}

.key-input::placeholder {
  color: var(--color-text-light);
  opacity: 0.7;
}
</style>
