<script setup lang="ts">
const props = defineProps<{
  id?: string
  modelValue: string
  placeholder?: string
  class?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'input', event: Event): void
  (e: 'keydown', event: KeyboardEvent): void
}>()

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value

  emit('update:modelValue', value)
  emit('input', event)
}

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event)
}
</script>

<template>
  <input :id="id" type="text" :value="modelValue" @input="handleInput" @keydown="handleKeydown"
    :placeholder="placeholder" class="song-input" :class title="Song Title" />
</template>

<style>
.song-input {
  border: 1px solid transparent;
  background: none;
  border-radius: var(--radius-sm);
  transition: border-color var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.song-input:focus {
  border-color: var(--color-primary-light);
  outline: none;
}

.song-input::placeholder {
  color: var(--color-text-light);
  opacity: 0.7;
}
</style>
