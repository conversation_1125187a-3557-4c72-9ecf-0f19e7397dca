<script setup lang="ts">
import { computed } from 'vue';

import { formatDuration } from '@/utils/duration';

interface Props {
  seconds: number
  warning?: boolean
  error?: boolean
  brackets?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  warning: false,
  error: false,
  brackets: false
});

const colorClass = computed(() => {
  if (props.error) return 'duration-error';
  if (props.warning) return 'duration-warning';
  return 'duration-normal';
});
</script>

<template>
  <span class="duration" :class="colorClass">
    {{ brackets ? '(' : null }}{{ formatDuration(seconds) }}{{ brackets ? ')' : null }}
  </span>
</template>

<style scoped>
.duration {
  font-family: var(--font-mono, monospace);
  font-size: var(--font-size-sm);
  padding: var(--space-2xs) var(--space-xs);
  border-radius: var(--radius-sm);
  background: var(--color-surface-muted);
}

.duration-normal {
  color: var(--color-duration);
}

.duration-warning {
  color: var(--color-duration-warning);
}

.duration-error {
  color: var(--color-duration-error);
}
</style>