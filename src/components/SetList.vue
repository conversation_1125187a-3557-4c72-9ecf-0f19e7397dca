<script setup lang="ts">
// This component will show a preview of the provided set list in an HTML canvas element
import { onMounted } from 'vue'
import { Song } from '@/types'

const props = defineProps<{
  setList: Song[]
}>()

onMounted(() => {
  console.log(props.setList)
})
</script>

<template>
  <div>
    <p>Set List Preview</p>
    <canvas class="setlist-canvas" />
  </div>
</template>

<style scoped>
.setlist-canvas {
  width: 100%;
  height: 100%;
}
</style>