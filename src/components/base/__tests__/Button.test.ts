import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import Button from '../Button.vue'

describe('Button.vue', () => {
  it('renders slot content', () => {
    const wrapper = mount(Button, {
      slots: {
        default: 'Click me'
      }
    })
    expect(wrapper.text()).toContain('Click me')
  })

  it('emits click event when clicked', async () => {
    const wrapper = mount(Button)
    await wrapper.trigger('click')
    expect(wrapper.emitted()).toHaveProperty('click')
  })

  it('applies variant classes correctly', () => {
    const wrapper = mount(Button, {
      props: {
        variant: 'primary'
      }
    })
    expect(wrapper.classes()).toContain('base-button--primary')
  })

  it('applies size classes correctly', () => {
    const wrapper = mount(Button, {
      props: {
        size: 'compact'
      }
    })
    expect(wrapper.classes()).toContain('base-button--compact')
  })

  it('is disabled when disabled prop is true', () => {
    const wrapper = mount(But<PERSON>, {
      props: {
        disabled: true
      }
    })
    expect(wrapper.attributes('disabled')).toBeDefined()
    expect(wrapper.classes()).toContain('base-button--disabled')
  })
})
