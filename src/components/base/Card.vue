<script setup lang="ts">
import { computed } from 'vue'

// Add component name for devtools and debugging
defineOptions({
  name: 'BaseCard'
})

interface Props {
  size?: 'default' | 'compact' | 'tiny'
  variant?: '' | 'primary' | 'secondary' | 'danger' | 'ghost'
  invert?: boolean
  shadow?: boolean
  justify?: 'start' | 'center' | 'end'
  selected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  variant: '',
  invert: false,
  shadow: false,
  justify: 'start',
  selected: false
})

const buttonClasses = computed(() => ({
  'card': true,
  [`card--${props.variant}`]: true,
  [`card--${props.size}`]: true,
  'invert': props.invert,
  'shadow': props.shadow,
  'justify': props.justify,
  'selected': props.selected
}))
</script>

<template>
  <div :class="buttonClasses">
    <slot />
  </div>
</template>

<style scoped>
:where(.card) {
  background-color: white;
  display: grid;
  gap: var(--space-sm);
  justify-items: v-bind(justify);
  border-radius: var(--radius-md);
  border: 1px solid rgb(from currentColor r g b / 0.15);
  padding: var(--space-md);
  transition: box-shadow var(--transition-normal);

  &.invert {
    background-color: var(--color-surface);
    border: 1px solid rgb(from currentColor r g b / 0.15);
  }

  &.shadow {
    box-shadow: var(--shadow-md);

    &:hover {
      box-shadow: var(--shadow-hover-md);
    }
  }

  &.card--tiny {
    padding: var(--space-xs);
  }

  &.card--compact {
    padding: var(--space-sm);
  }
}
</style>