<script setup lang="ts">
import { computed } from 'vue'

// Add component name for devtools and debugging
defineOptions({
  name: 'BaseButton'
})

interface Props {
  size?: 'default' | 'compact' | 'tiny'
  variant?: '' | 'primary' | 'secondary' | 'danger' | 'ghost'
  type?: 'button' | 'submit' | 'reset'
  invert?: boolean
  shadow?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  variant: '',
  type: 'button',
  invert: false,
  shadow: false,
  disabled: false
})

const buttonClasses = computed(() => ({
  'base-button': true,
  [`base-button--${props.variant}`]: true,
  [`base-button--${props.size}`]: true,
  'base-button--inverted': props.invert,
  'base-button--shadow': props.shadow,
  'base-button--disabled': props.disabled
}))
</script>

<template>
  <button :type="type" :class="buttonClasses" :disabled="disabled">
    <slot />
  </button>
</template>

<style scoped>
.base-button {
  --button-bg: var(--color-surface-muted);
  --button-color: var(--color-text);
  --button-border: currentColor;
  --button-border: rgb(from currentColor r g b / 0.5);
  --button-hover-bg: var(--color-surface-dark);
  --button-hover-color: var(--color-text-white);
  --button-hover-border: var(--color-surface-dark);

  display: inline-flex;
  gap: var(--space-xs);
  align-items: center;
  justify-content: center;
  font-weight: 500;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  cursor: pointer;
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-base);
  color: var(--button-color);
  background-color: var(--button-bg);
  border: 1px solid var(--button-border);

  &.base-button--shadow {
    box-shadow: var(--shadow-sm);

    &:hover {
      box-shadow: var(--shadow-hover-sm);
    }
  }

  &:hover {
    background-color: var(--button-hover-bg);
    color: var(--button-hover-color);
    border-color: var(--button-hover-border);
  }
}

/* Compact variant */
.base-button--compact {
  padding: var(--space-2xs) var(--space-sm);
  font-size: var(--font-size-sm);
}

/* Tiny variant */
.base-button--tiny {
  padding: var(--space-2xs);
  font-size: var(--font-size-xs);
}

/* Color variants */
.base-button--primary {
  --button-bg: var(--color-primary);
  --button-color: var(--color-text-white);
  --button-hover-bg: var(--color-primary-dark);

  &.base-button--inverted {
    --button-bg: var(--color-text-white);
    --button-color: var(--color-primary);
    --button-hover-bg: var(--color-primary-light);
    --button-hover-color: var(--color-text-white);
  }
}

.base-button--secondary {
  --button-bg: var(--color-secondary);
  --button-color: var(--color-text-white);
  --button-hover-bg: var(--color-secondary-dark);

  &.base-button--inverted {
    --button-bg: var(--color-text-white);
    --button-color: var(--color-secondary);
    --button-hover-bg: var(--color-secondary-light);
    --button-hover-color: var(--color-text-black);
    /* --button-hover-color: var(--color-text-white); */
  }
}

.base-button--danger {
  --button-bg: var(--color-error);
  --button-color: var(--color-text-white);
  --button-hover-bg: var(--color-error-dark);

  &.base-button--inverted {
    --button-bg: var(--color-text-white);
    --button-color: var(--color-error);
    --button-hover-bg: var(--color-error-muted);
    --button-hover-color: var(--color-text-white);
  }
}

.base-button--ghost {
  --button-bg: transparent;
  --button-color: var(--color-text-muted);
  --button-hover-bg: var(--color-surface-light);
  --button-hover-color: var(--color-text-muted);
  --button-hover-border: currentColor;

  &.base-button--inverted {
    --button-bg: var(--color-text-white);
    --button-color: var(--color-primary);
    --button-hover-bg: var(--color-primary-light);
    --button-hover-color: var(--color-text-white);
  }
}

/* Disabled state */
.base-button--disabled,
.base-button--disabled:hover {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--color-text-muted);
  color: var(--color-text-light);
  border-color: var(--color-text-muted);
}

/* Focus styles */
.base-button:focus {
  outline: 2px solid var(--color-primary-light);
  outline-offset: 2px;
}
</style>