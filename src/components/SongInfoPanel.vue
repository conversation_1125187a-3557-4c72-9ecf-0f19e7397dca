<script setup lang="ts">
import { Song } from '@/models/Song'

defineProps<{
    song?: Song
}>()
</script>

<template>
    <div class="song-info-panel">
        <h2>Song Info</h2>
        <div v-if="song">
            <p><strong>Title:</strong> {{ song.title }}</p>
            <p v-if="song.artist"><strong>Artist:</strong> {{ song.artist }}</p>
            <p v-if="song.key"><strong>Key:</strong> {{ song.key }}</p>
            <p v-if="song.durationSecs"><strong>Duration:</strong> {{ song.formattedDuration }}</p>
        </div>
        <div v-else>
            <p>No song selected</p>
        </div>
    </div>
</template>
