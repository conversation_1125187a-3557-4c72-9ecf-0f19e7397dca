<script setup lang="ts">
import { Eye, Pencil, Trash, Cloud, HardDrive } from 'lucide-vue-next'
import { format } from 'date-fns'
import type { StoredShow } from '@/types/'
import SetDurationDisplay from '@/components/SetDurationDisplay.vue'

defineProps<{
  show: StoredShow
  selected?: boolean
  isPreviewOpen?: boolean
  isAuthenticated?: boolean
}>()

const getSongSets = (show: StoredShow) => {
  if (show.sets?.length === 0) return []
  return show.sets.map(set => set.songs)
}

const formatDate = (date: string) => {
  if (typeof date !== 'string') {
    console.log('Invalid date format')
    return 'dd MMM yyyy'
  }

  if (!date) return ''

  return format(new Date(date), 'dd MMM yyyy')
}
</script>

<template>
  <BaseCard class="show-summary" :selected @click="$emit('select', show.id)">
    <div v-if="show.sets.length > 0" class="show-info">
      <h2>
        <RouterLink :to="`/show/${show.id}`">{{ show.title ? show.title : 'Untitled Show' }}</RouterLink>
      </h2>
      <p class="show-act">{{ show.act }}</p>
      <h3 class="show-venue">
        {{ show.venue }}
      </h3>
      <p class="show-date">{{ formatDate(show.date) }}</p>
      <div class="show-metadata">
        {{ show.sets.length }} sets - {{ getSongSets(show).flat().length }} songs
        <SetDurationDisplay :set="getSongSets(show).flat()" brackets />
      </div>
    </div>
    <div v-if="show.sets.length > 0" class="actions">
      <BaseButton v-if="show.storageLocation === 'local'" size="tiny" invert shadow
        @click.stop="$emit('migrate', show.id)" title="Save to cloud">
        <Cloud :size="16" />
      </BaseButton>
      <BaseButton size="tiny" variant="primary" shadow invert @click.stop="$emit('preview', show.id)"
        :class="{ 'active': isPreviewOpen }">
        <Eye :size="16" />
      </BaseButton>
      <BaseButton size="tiny" variant="secondary" shadow invert @click="$emit('edit', show.id)">
        <Pencil :size="16" />
      </BaseButton>
      <BaseButton size="tiny" variant="danger" shadow invert @click="$emit('delete', show.id)">
        <Trash :size="16" />
      </BaseButton>
    </div>
  </BaseCard>
</template>

<style scoped>
.show-summary {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  display: grid;
  gap: var(--space-sm);

  .actions {
    display: flex;
    justify-self: flex-end;
    align-self: flex-end;
    gap: var(--space-sm);
    opacity: 0.5;
    transition: opacity var(--transition-slow);
  }

  &:hover .actions {
    opacity: 1;
  }

  &.selected {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border: 1px solid var(--color-accent);

    .actions {
      opacity: 1;
    }
  }
}

.show-info {
  margin-bottom: var(--space-sm);
}

.show-venue {
  display: flex;
  align-items: center;
}

.show-metadata {
  display: flex;
  gap: var(--space-xs);
  font-size: var(--font-size-sm);
  color: var(--color-text-light);
  margin-block-start: var(--space-xs);
}

.show-act {
  font-weight: bold;
  color: var(--color-text-light);
  font-size: var(--font-size-md);
}

.show-date {
  font-size: var(--font-size-sm);
  color: var(--color-text);
}

.action-button {
  padding: var(--space-xs);
  border-radius: var(--radius-sm);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.action-button.preview {
  color: var(--color-info);
  background-color: var(--color-text-white);
}

.action-button.preview.active {
  background-color: var(--color-info);
  color: var(--color-text-white);
  box-shadow: var(--shadow-md);
  transform: scale(1.1);
}

.action-button.edit {
  color: var(--color-primary);
  background-color: var(--color-text-white);
}

.action-button.delete {
  color: var(--color-error);
  background-color: var(--color-text-white);
}

.action-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.action-button.active:hover {
  transform: scale(1.05);
}

.storage-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: var(--space-xs);
  color: var(--color-text-muted);

  &:hover {
    color: var(--color-text-light);
  }
}

.action-button.migrate {
  color: var(--color-accent);
  background-color: var(--color-text-white);
}
</style>