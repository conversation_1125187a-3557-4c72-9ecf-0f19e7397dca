<script setup lang="ts">
import { computed, ref } from 'vue'
import draggable from 'vuedraggable'
import { GripVertical, Trash, ArrowLeft, ArrowRight, Settings, Save, Loader2 } from 'lucide-vue-next'
import { useDebouncedSave } from '@/composables/useDebouncedSave'
import SetDurationDisplay from '@/components/SetDurationDisplay.vue'
import SongInput from '@/components/SongInput.vue'
import KeyInput from '@/components/KeyInput.vue'
import type { Song } from '@/types/'
import { createSong } from '@/utils/entityFactory'
import { getDurationParts, parseDurationInput } from '@/utils/duration'

const props = defineProps<{
  modelValue: Song[]
  setIndex: number
  setTitle?: string
  duplicateSongs?: Set<string>
  isLastSet?: boolean
}>()

const emit = defineEmits<{
  (e: 'updateSong', setIndex: number, songIndex: number, field: 'title' | 'key' | 'artist' | 'durationSecs' | 'bpm', value: string): void
  (e: 'addSong', setIndex: number, songIndex?: number): void
  (e: 'removeSong', setIndex: number, songIndex: number): void
  (e: 'addSet'): void
  (e: 'removeSet', setIndex: number): void
  (e: 'update:modelValue', songs: Song[]): void
  (e: 'moveSong', fromSetIndex: number, fromSongIndex: number, toSetIndex: number, toStart: boolean): void
  (e: 'songSettings', setIndex: number, songIndex: number): void
  (e: 'updateSetTitle', setIndex: number, title: string): void
}>()

// Handle drag and drop updates
const updateSongs = (newSongs: Song[]) => {
  if (!props.isLastSet) {
    emit('update:modelValue', newSongs.map(song => {
      if (!song.id) {
        return createSong(song)
      }
      return song
    }))
    return
  }

  // Filter out the separator and update isLastSong based on separator position
  const separatorIndex = newSongs.findIndex(song => song.id === encoreSeparatorId)
  const songsWithoutSeparator = newSongs.filter(song => song.id !== encoreSeparatorId)

  const updatedSongs = songsWithoutSeparator.map((song, index) => {
    if (!song.id) {
      return createSong({
        ...song,
        isLastSong: index === separatorIndex - 1
      })
    }
    return {
      ...song,
      isLastSong: index === separatorIndex - 1
    }
  })

  emit('update:modelValue', updatedSongs)
}

// Handle keyboard navigation locally
const handleKeyDown = async (e: KeyboardEvent, songIndex: number, field: 'title' | 'key') => {
  // Handle Cmd/Ctrl + Enter to add new set
  if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
    e.preventDefault()
    emit('addSet')
    return
  }

  // Handle Enter key
  if (e.key === 'Enter') {
    e.preventDefault()
    const currentSong = props.modelValue[songIndex]

    if (field === 'title') {
      // Focus key input of the same song
      const keyInput = document.querySelector<HTMLInputElement>(`#key-${currentSong.id}`)
      keyInput?.focus()
    } else {
      // If this is an empty song (no title and no key), focus its title input
      if (!currentSong.title.trim() && !currentSong.key.trim()) {
        const titleInput = document.querySelector<HTMLInputElement>(`#title-${currentSong.id}`)
        titleInput?.focus()
        return
      } else {
        // Remove focus from the key input
        const keyInput = document.querySelector<HTMLInputElement>(`#key-${currentSong.id}`)
        keyInput?.blur()
      }
    }
  }

  // Handle escape key (Remove focus from the current input)
  if (e.key === 'Escape') {
    e.preventDefault()
    const currentSong = props.modelValue[songIndex]

    const titleInput = document.querySelector<HTMLInputElement>(`#title-${currentSong.id}`)
    titleInput?.blur()
    const keyInput = document.querySelector<HTMLInputElement>(`#key-${currentSong.id}`)
    keyInput?.blur()
  }
}

// Compute if a song is a duplicate
const isDuplicateSong = (setIndex: number, songIndex: number) => {
  // If this is the last set and we're past the encore separator, adjust the index
  let actualSongIndex = songIndex
  if (props.isLastSet) {
    const separatorIndex = props.modelValue.findIndex(s => s.isLastSong)
    if (separatorIndex !== -1 && songIndex > separatorIndex + 1) {
      actualSongIndex = songIndex - 1 // Adjust for the separator
    }
  }

  const song = props.modelValue[actualSongIndex]
  if (!song?.title.trim()) return false
  return props.duplicateSongs?.has(song.title.toLowerCase().trim()) || false
}

const encoreSeparatorId = 'encore-separator'

const songSettingsOpen = ref<Record<number, boolean>>({})
const localSongChanges = ref<Record<number, Partial<Song>>>({})
const hasUnsavedChanges = computed(() => Object.keys(localSongChanges.value).length > 0)

const { debouncedSave, isSaving } = useDebouncedSave(1000) // 1 second delay

// Convert duration seconds to minutes and seconds for display
const getDurationDisplay = (songIndex: number) => {
  const durationSecs = getCurrentValue(songIndex, 'durationSecs') as number | undefined
  return getDurationParts(durationSecs)
}

// Handle duration input changes
const handleDurationChange = (songIndex: number, field: 'minutes' | 'seconds', value: string) => {
  const currentParts = getDurationDisplay(songIndex)
  const newDurationSecs = parseDurationInput(
    field === 'minutes' ? value : currentParts.minutes,
    field === 'seconds' ? value : currentParts.seconds
  )
  updateLocalSongChanges(songIndex, 'durationSecs', newDurationSecs)
}

// Handle local changes for any song field
const updateLocalSongChanges = (songIndex: number, field: keyof Song, value: any) => {
  if (!localSongChanges.value[songIndex]) {
    localSongChanges.value[songIndex] = {}
  }
  localSongChanges.value[songIndex] = {
    ...localSongChanges.value[songIndex],
    [field]: value
  }
}

// Save changes for a specific song
const saveSongChanges = async (songIndex: number) => {
  const changes = localSongChanges.value[songIndex]
  if (!changes) return

  const songId = props.modelValue[songIndex].id

  // Create a save operation that will emit all changes
  const saveOperation = async () => {
    const allowedFields = ['title', 'key', 'artist', 'durationSecs', 'bpm'] as const
    Object.entries(changes).forEach(([field, value]) => {
      if (allowedFields.includes(field as any)) {
        emit('updateSong',
          props.setIndex,
          songIndex, field as typeof allowedFields[number],
          value.toString())
      }
    })
  }

  // Trigger debounced save
  await debouncedSave(songId, saveOperation)

  // Clear local changes for this song
  delete localSongChanges.value[songIndex]
}

// Get current value for a song field (local change or original)
const getCurrentValue = (songIndex: number, field: keyof Song) => {
  const changes = localSongChanges.value[songIndex]
  return changes && field in changes
    ? changes[field]
    : props.modelValue[songIndex][field]
}

const songsWithSeparator = computed(() => {
  if (!props.isLastSet) return props.modelValue

  const separator = {
    id: encoreSeparatorId,
    title: 'Encores',
    key: '',
    isEncoreSeparator: true
  }

  // Always add the separator at the end if there's no last song marked
  const lastSongIndex = props.modelValue.findIndex(song => song.isLastSong)
  const insertIndex = lastSongIndex === -1 ? props.modelValue.length : lastSongIndex + 1

  const result = [...props.modelValue]
  result.splice(insertIndex, 0, separator)
  return result
})

</script>

<template>
  <div class="set-container">
    <div class="set-number">Set {{ setIndex + 1 }}</div>
    <div class="set-header">
      <div class="set-title">
        <input type="text" :value="setTitle || ''"
          @input="e => emit('updateSetTitle', setIndex, (e.target as HTMLInputElement).value)" placeholder="Set Title"
          class="set-title-input" />
        <SetDurationDisplay :set="modelValue" showCount />
      </div>
      <button @click="emit('removeSet', setIndex)" class="delete" title="Remove set">
        <Trash :size="16" />
      </button>
    </div>

    <draggable :model-value="isLastSet ? songsWithSeparator : modelValue" @update:model-value="updateSongs"
      item-key="id" handle=".drag-handle" class="song-list">
      <template #item="{ element: song, index: songIndex }">
        <div v-if="song.isEncoreSeparator" class="encore-separator" :class="{ 'is-dragging': false }">
          <div class="drag-handle">
            <div class="separator-label">Encores</div>
          </div>
        </div>
        <div v-else class="song-item" :class="{
          'duplicate-song': isDuplicateSong(setIndex, songIndex),
          'is-encore': isLastSet && songIndex > songsWithSeparator.findIndex(s => s.isEncoreSeparator)
        }">
          <div class="song-item__controls">
            <BaseButton v-if="setIndex > 0" size="tiny" variant="ghost" class="move-button song-item__control"
              title="Move to previous set" @click="emit('moveSong', setIndex, songIndex, setIndex - 1, false)">
              <ArrowLeft :size="16" />
            </BaseButton>
            <div class="drag-handle song-item__control">
              <GripVertical :size="16" />
            </div>
          </div>
          <div class="song-details">
            <div class="song-inputs">
              <SongInput :id="'title-' + song.id" v-model="song.title" placeholder="Song" class="song-title-input"
                @keydown="(e) => handleKeyDown(e, songIndex, 'title')"
                @update:modelValue="(value) => emit('updateSong', setIndex, songIndex, 'title', value)" />
              <KeyInput :id="'key-' + song.id" v-model="song.key" placeholder="Key" class="song-key-input"
                @keydown="(e) => handleKeyDown(e, songIndex, 'key')"
                @update:modelValue="(value) => emit('updateSong', setIndex, songIndex, 'key', value)" />
            </div>
            <div v-if="songSettingsOpen[songIndex]" class="song-metadata">
              <input type="text" :id="'artist-' + song.id" :value="getCurrentValue(songIndex, 'artist')"
                placeholder="Artist" class="song-artist-input" @input="(e: Event) => {
                  const target = e.target as HTMLInputElement;
                  updateLocalSongChanges(songIndex, 'artist', target.value);
                }" />
              <div class="song-duration">
                <label :for="'duration-mins-' + song.id">Duration:</label>
                <input type="number" :id="'duration-mins-' + song.id" :value="getDurationDisplay(songIndex).minutes"
                  min="0" max="59" class="duration-input" @input="(e: Event) => {
                    const target = e.target as HTMLInputElement;
                    handleDurationChange(songIndex, 'minutes', target.value);
                  }" />
                <span>:</span>
                <input type="number" :id="'duration-secs-' + song.id" :value="getDurationDisplay(songIndex).seconds"
                  min="0" max="59" class="duration-input" @input="(e: Event) => {
                    const target = e.target as HTMLInputElement;
                    handleDurationChange(songIndex, 'seconds', target.value);
                  }" />
              </div>
              <div class="song-bpm">
                <label :for="'bpm-' + song.id">BPM:</label>
                <input type="number" :id="'bpm-' + song.id" :value="getCurrentValue(songIndex, 'bpm')" min="0" max="300"
                  class="bpm-input" @input="(e: Event) => {
                    const target = e.target as HTMLInputElement;
                    updateLocalSongChanges(songIndex, 'bpm', target.valueAsNumber);
                  }" />
              </div>
              <BaseButton v-if="localSongChanges[songIndex]" size="tiny" variant="success" class="save-changes"
                title="Save changes" :disabled="isSaving[props.modelValue[songIndex].id]"
                @click="saveSongChanges(songIndex)">
                <Loader2 v-if="isSaving[props.modelValue[songIndex].id]" :size="16" class="animate-spin" />
                <Save v-else :size="16" />
                <span class="button-text">
                  {{ isSaving[props.modelValue[songIndex].id] ? 'Saving...' : 'Save' }}
                </span>
              </BaseButton>
            </div>
          </div>
          <div class="song-item__controls align-top">
            <BaseButton size="tiny" variant="secondary"
              @click="songSettingsOpen[songIndex] = !songSettingsOpen[songIndex]"
              class="song-settings song-item__control" title="Song settings">
              <Settings :size="16" />
            </BaseButton>
            <BaseButton size="tiny" variant="danger" @click="emit('removeSong', setIndex, songIndex)"
              class="remove-song delete song-item__control" title="Remove song">
              <Trash :size="16" />
            </BaseButton>
            <BaseButton v-if="!isLastSet && setIndex < 3" size="tiny" variant="ghost"
              class="move-button song-item__control" title="Move to next set"
              @click="emit('moveSong', setIndex, songIndex, setIndex + 1, true)">
              <ArrowRight :size="16" />
            </BaseButton>
          </div>
          <div v-if="song.title || song.key" class="add-song-inline-button"
            @click="emit('addSong', setIndex, songIndex)">
          </div>
        </div>
      </template>
    </draggable>
    <div class="set-footer">
      <BaseButton size="compact" @click="emit('addSong', setIndex)" class="add-song">
        + Add Song
      </BaseButton>
    </div>
  </div>
</template>

<style scoped>
.set-container {
  max-width: 500px;
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--space-sm);
  box-shadow: var(--shadow-md);
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.set-number {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--color-text);
  padding: 0 var(--space-sm);
}

.set-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-sm);
  gap: var(--space-sm);
  padding: var(--space-sm);
  border-bottom: 1px solid var(--color-border);
}

.set-title {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  flex: 1;
}

.set-title-input {
  font-size: var(--font-size-sm);
  padding: var(--space-xs) var(--space-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-surface);
  color: var(--color-text);
  width: 100%;
  max-width: 300px;
  transition: all var(--transition-normal);
}

.set-title-input::placeholder {
  color: var(--color-text-light);
}

.set-title-input:hover {
  border-color: var(--color-primary);
}

.set-title-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
}

.song-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2xs);
}

.drag-handle {
  color: var(--color-text-muted);
  cursor: grab;
}

.song-inputs {
  display: grid;
  grid-template-columns: 1fr 5.5rem;
  gap: var(--space-xs);
}

.song-title-input {
  min-width: 0;
  font-size: var(--font-size-xs);
}

.song-key-input {
  font-size: var(--font-size-xs);
  text-align: center;
}

.delete {
  color: var(--color-error);
  border: none;
  background: none;
  cursor: pointer;
  padding: 0;
}

.set-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-xs);
}

.add-song {
  background: var(--color-primary);
  color: var(--color-text-white);
  border-color: transparent;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  width: fit-content;
  opacity: 0.9;
}

.add-song:hover {
  opacity: 1;
}

.song-item {
  gap: 0 var(--space-xs);
  padding-inline: var(--space-xs);
  border-radius: var(--radius-md);
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
}

.song-item__controls {
  display: flex;
  gap: var(--space-xs);
}

.add-song-inline-button {
  cursor: pointer;
  grid-column: 1 / -1;
  width: 100%;
  height: 2px;
  text-align: center;
  align-items: center;
  background-color: transparent;
  color: white;
  opacity: .5;
  padding: 0;
  position: relative;
  transition: padding var(--transition-slow), opacity var(--transition-slow), background-color var(--transition-slow);

  &:hover {
    padding-block: .3em;
    opacity: 1;

    &::after {
      position: absolute;
      display: block;
      content: '+ add song';
      font-size: var(--font-size-lg);
      top: 50%;
      left: 50%;
      transform: translate(-50%, -55%);
      background-color: var(--color-primary);
      border-radius: var(--radius-sm);
    }
  }
}

.song-item:focus-within .add-song-inline-button,
.song-item:hover .add-song-inline-button {
  padding-block: .3em;
  opacity: 1;

  &::after {
    position: absolute;
    display: block;
    font-size: var(--font-size-base);
    content: '+ add song';
    top: 50%;
    left: 50%;
    padding: .3em .5em;
    transform: translate(-50%, -55%);
    background-color: var(--color-primary);
    border-radius: var(--radius-sm);
  }

}



.song-item.duplicate-song {
  background: var(--color-warning-light);
  border: 1px solid var(--color-warning);
}

.song-item.duplicate-song:hover {
  background: var(--color-warning-light);
}

.song-item.duplicate-song .song-inputs input {
  background: var(--color-warning-light);
}

.song-item.duplicate-song .song-title-input {
  background: var(--color-error-light);
  border-color: var(--color-error);
}

.encore-separator {
  display: flex;
  align-items: center;
  height: 4px;
  margin: var(--space-sm) 0;
  background: var(--color-accent);
  position: relative;
}

.encore-separator .drag-handle {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  height: var(--font-size-base);
  cursor: grab;
}

.encore-separator .separator-label {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-surface);
  padding: 0 var(--space-xs);
  font-size: var(--font-size-2xs);
  color: var(--color-accent);
  white-space: nowrap;
}

.encore-separator .drag-handle:active {
  cursor: grabbing;
}

.encore-separator:active {
  background: var(--color-accent-dark);
}

.song-item.is-encore {
  background: var(--color-accent-light);
}

.song-item.is-encore:nth-child(even) {
  background: var(--color-accent-muted);
}


.align-top {
  align-self: flex-start;
}

.move-button {
  color: var(--color-text-muted);
  padding: var(--space-2xs);
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast), background-color var(--transition-fast);
}

.move-button:hover {
  color: var(--color-text);
  background-color: var(--color-surface-dark);
}

.song-item__control {
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.song-item:hover .song-item__control,
.song-item:focus-within .song-item__control {
  opacity: 1;
}

.song-duration {
  display: flex;
  align-items: center;
  gap: var(--space-2xs);
}

.song-duration input {
  width: 4rem;
  text-align: center;
}

.song-duration span {
  color: var(--color-text-muted);
  user-select: none;
}

.song-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
  align-items: center;

  .song-artist-input {
    flex-basis: 100%;
  }

  .song-duration {
    flex-basis: 30%;

    >* {
      max-width: 5rem;
    }
  }

  .song-tempo-input {
    flex-basis: 20%;
    max-width: 5rem;
  }
}

.save-changes {
  opacity: 0.8;
  transition: opacity var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-2xs);
}

.save-changes:not(:disabled):hover {
  opacity: 1;
}

.save-changes:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.button-text {
  margin-left: var(--space-2xs);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>