<script setup lang="ts">
import { h } from 'vue'
import { LogOut, User } from 'lucide-vue-next'
import BaseDropdown from '@/components/BaseDropdown.vue'
import { useAuth } from '@/composables/useAuth'

const { user, logout } = useAuth()

const handleLogout = async () => {
  await logout()
}

const UserButton = () => {
  return h('button', { class: 'dropdown-trigger' }, [
    h(User, { class: 'user-icon' }),
  ])
}
</script>

<template>
  <header>
    <div class="header-content">
      <h1>
        <RouterLink to="/">Set List Creator</RouterLink>
      </h1>

      <div class="auth-controls">
        <RouterLink to="/" class="nav-link">Home</RouterLink>
        <RouterLink :to="{ name: 'shows' }" class="nav-link">Shows</RouterLink>
        <template v-if="user">
          <RouterLink :to="{ name: 'setlists' }" class="nav-link">Set Lists</RouterLink>
          <RouterLink :to="{ name: 'songs' }" class="nav-link">Songs</RouterLink>
          <RouterLink :to="{ name: 'venues' }" class="nav-link">Venues</RouterLink>
          <BaseDropdown :trigger="UserButton">
            <nav class="user-menu">
              <RouterLink :to="{ name: 'user' }" class="menu-item">
                <User class="icon" />
                Profile
              </RouterLink>
              <button class="menu-item" @click="handleLogout">
                <LogOut class="icon" />
                Logout
              </button>
            </nav>
          </BaseDropdown>
        </template>
        <RouterLink v-else to="/login" class="nav-link">Login</RouterLink>
      </div>
    </div>
  </header>
</template>

<style scoped>
header {
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
}

.header-content {
  max-width: var(--container-max-width);
  margin-inline: auto;
  padding: var(--space-md) var(--space-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dropdown {
  margin-left: 1em;
}

h1 {
  margin: 0;
  font-size: var(--font-size-lg);

  a {
    color: inherit;
    text-decoration: none;
  }
}

.auth-controls {
  display: flex;
  align-items: center;
}

.nav-link {
  position: relative;
  color: var(--color-text);
  text-decoration: none;
  font-weight: 500;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast), background-color var(--transition-fast);

  &::after {
    content: '';
    position: absolute;
    left: var(--space-sm);
    right: var(--space-sm);
    bottom: 0;
    height: 2px;
    background-color: var(--color-primary);
    transform: scaleX(0);
    transition: transform var(--transition-fast);
    transform-origin: center;
  }

  &:hover {
    color: var(--color-primary);
    background-color: var(--color-surface-hover);
  }

  &.router-link-active {
    color: var(--color-primary);

    &::after {
      transform: scaleX(1);
    }
  }
}

.user-menu {
  padding: 5px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-xs) var(--space-sm);
  color: var(--color-text);
  text-decoration: none;
  border: none;
  background: none;
  width: 100%;
  font-size: var(--font-size-base);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background-color var(--transition-fast);

  &:hover {
    background-color: var(--color-surface-hover);
  }

  .icon {
    color: var(--color-text-muted);
  }
}
</style>