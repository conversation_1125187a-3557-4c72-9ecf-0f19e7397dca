<script setup lang="ts">
import { ref } from 'vue'
import { Plus, X, Mail } from 'lucide-vue-next'
import BaseButton from '@/components/base/Button.vue'

interface Collaborator {
  email: string
  role: 'editor' | 'viewer'
}

const props = defineProps<{
  collaborators: Collaborator[]
}>()

const emit = defineEmits<{
  (e: 'add', collaborator: Collaborator): void
  (e: 'remove', email: string): void
}>()

const newEmail = ref('')
const newRole = ref<'editor' | 'viewer'>('editor')

const handleAdd = () => {
  if (newEmail.value) {
    emit('add', {
      email: newEmail.value,
      role: newRole.value
    })
    newEmail.value = ''
  }
}

const handleRemove = (email: string) => {
  emit('remove', email)
}
</script>

<template>
  <div class="collaborators">
    <div class="collaborator-form">
      <div class="input-group">
        <div class="form-group">
          <input
            type="email"
            v-model="newEmail"
            placeholder="Email address"
            @keyup.enter="handleAdd"
          >
        </div>
        <div class="form-group">
          <select v-model="newRole">
            <option value="editor">Editor</option>
            <option value="viewer">Viewer</option>
          </select>
        </div>
      </div>
      <BaseButton @click="handleAdd" size="compact">
        <Plus class="icon" />
        Add
      </BaseButton>
    </div>

    <ul class="collaborators-list" v-if="collaborators.length">
      <li v-for="collaborator in collaborators" :key="collaborator.email" class="collaborator-item">
        <div class="collaborator-info">
          <Mail class="icon" />
          <span>{{ collaborator.email }}</span>
          <span class="role-badge" :class="collaborator.role">
            {{ collaborator.role }}
          </span>
        </div>
        <BaseButton
          @click="handleRemove(collaborator.email)"
          variant="ghost"
          size="compact"
          class="remove-button"
        >
          <X class="icon" />
        </BaseButton>
      </li>
    </ul>
    <p v-else class="empty-state">No collaborators added yet</p>
  </div>
</template>

<style scoped>
.collaborators {
  display: grid;
  gap: var(--space-md);
}

.collaborator-form {
  display: flex;
  gap: var(--space-sm);
}

.input-group {
  display: flex;
  gap: var(--space-sm);
  flex: 1;
}

.form-group {
  flex: 1;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: var(--space-xs);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-surface);
}

.collaborators-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  gap: var(--space-xs);
}

.collaborator-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-xs);
  background: var(--color-surface-light);
  border-radius: var(--radius-sm);
}

.collaborator-info {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.role-badge {
  font-size: var(--font-size-sm);
  padding: 2px var(--space-xs);
  border-radius: var(--radius-sm);
  text-transform: capitalize;
}

.role-badge.editor {
  background: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.role-badge.viewer {
  background: var(--color-surface-muted);
  color: var(--color-text-muted);
}

.empty-state {
  color: var(--color-text-muted);
  text-align: center;
  margin: 0;
}

.icon {
  width: 1.2em;
  height: 1.2em;
}

.remove-button {
  color: var(--color-text-muted);
}

.remove-button:hover {
  color: var(--color-error);
}
</style>
