import { ref, onMounted, watch } from 'vue'
import type { Show, StoredShow } from '@/types/'
import { useAuth } from '@/composables/useAuth'
import { db } from '@/firebase'

import {
  doc,
  collection,
  addDoc,
  Timestamp,
  query,
  where,
  getDocs,
  serverTimestamp,
  getDoc,
  deleteDoc
} from 'firebase/firestore'
import type { DocumentReference, DocumentData } from 'firebase/firestore'

interface FirebaseSet extends DocumentData {
  name: string
  songs: {
    songId: DocumentReference
    order: number
    isLastSong: boolean
  }[]
  createdBy: string
  createdAt: Timestamp
  updatedAt: Timestamp
}

interface FirebaseSetList extends DocumentData {
  name: string
  sets: {
    setId: DocumentReference
    order: number
  }[]
  createdBy: string
  createdAt: Timestamp
  updatedAt: Timestamp
  lastUsed: Timestamp
}

interface FirebaseSong extends DocumentData {
  title: string
  key: string
  artist?: string
  durationSecs?: number
  bpm?: number
  notes?: string
  lastPerformed?: Timestamp
  timesPerformed: number
  createdBy: string
  createdAt: Timestamp
  updatedAt: Timestamp
}

interface FirebaseShow extends DocumentData {
  title: string
  venue: string
  act: string
  date: Timestamp
  setListId: DocumentReference
  notes?: string
  createdBy: string
  createdAt: Timestamp
  updatedAt: Timestamp
}

const STORAGE_KEY = 'show-setlists'

export function useShowStorage() {
  const shows = ref<StoredShow[]>([])
  const currentShow = ref<StoredShow | null>(null)
  const { user } = useAuth()

  // This function currently fetches all shows from local storage
  function getAllShows(): StoredShow[] {
    try {
      const data = localStorage.getItem(STORAGE_KEY)
      const parsedShows = data ? JSON.parse(data) : []

      return parsedShows.map((show: StoredShow) => ({
        ...show,
        storageLocation: show.storageLocation || 'local'
      }))
    } catch {
      return []
    }
  }

  function saveShow(
    showData: Omit<Show, 'id' | 'storageLocation'>
  ): StoredShow {
    const newShow: StoredShow = {
      ...showData,
      storageLocation: 'local',
      id: crypto.randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    shows.value = [...shows.value, newShow]
    localStorage.setItem(STORAGE_KEY, JSON.stringify(shows.value))
    currentShow.value = newShow
    return newShow
  }

  function updateShow(id: string, showData: Show): StoredShow | null {
    const index = shows.value.findIndex(show => show.id === id)
    if (index === -1) return null

    const updatedShow: StoredShow = {
      ...showData,
      id,
      createdAt: shows.value[index].createdAt,
      updatedAt: new Date().toISOString(),
      storageLocation: shows.value[index].storageLocation
    }

    shows.value[index] = updatedShow
    localStorage.setItem(STORAGE_KEY, JSON.stringify(shows.value))
    currentShow.value = updatedShow
    return updatedShow
  }

  function deleteShow(id: string): boolean {
    if (!confirmShowDeletion(id)) return false

    const filteredShows = shows.value.filter(show => show.id !== id)
    if (filteredShows.length === shows.value.length) return false

    shows.value = filteredShows

    localStorage.setItem(STORAGE_KEY, JSON.stringify(shows.value))
    deleteDoc(doc(db, 'shows', id))

    if (currentShow.value?.id === id) {
      currentShow.value = null
    }

    return true
  }

  function confirmShowDeletion(id: string): boolean {
    return window.confirm('Are you sure you want to delete this show?')
  }

  function loadShow(id: string): StoredShow | null {
    const show = shows.value.find(show => show.id === id)
    if (!show) return null
    currentShow.value = show
    return show
  }

  async function migrateToCloud(
    show: StoredShow
  ): Promise<{ success: boolean; error?: string }> {
    if (!user.value) {
      return {
        success: false,
        error: 'User must be authenticated to migrate shows to cloud'
      }
    }

    try {
      const createdDocs: { collection: string; id: string }[] = []
      const songRefs = new Map<string, string>()

      // 1. Create or find existing songs
      for (const set of show.sets) {
        for (const song of set.songs) {
          if (songRefs.has(song.id)) continue

          // Check if song already exists
          const songsRef = collection(db, 'songs')
          const q = query(
            songsRef,
            where('title', '==', song.title),
            where('artist', '==', song.artist || ''),
            where('createdBy', '==', user.value.uid)
          )
          const querySnapshot = await getDocs(q)

          if (!querySnapshot.empty) {
            // Use existing song
            songRefs.set(song.id, querySnapshot.docs[0].id)
          } else {
            // Create new song
            const songDoc = await addDoc(songsRef, {
              title: song.title,
              key: song.key,
              artist: song.artist || '',
              durationSecs: song.durationSecs || 0,
              tempo: song.bpm || 0,
              notes: song.notes || '',
              lastPerformed: serverTimestamp(),
              timesPerformed: 0,
              createdBy: user.value.uid,
              createdAt: show.createdAt
                ? Timestamp.fromDate(new Date(show.createdAt))
                : serverTimestamp(),
              updatedAt: show.updatedAt
                ? Timestamp.fromDate(new Date(show.updatedAt))
                : serverTimestamp()
            })
            songRefs.set(song.id, songDoc.id)
            createdDocs.push({ collection: 'songs', id: songDoc.id })
          }
        }
      }

      // 2. Create sets with song references
      const setRefs = new Map<string, string>()
      for (const [index, set] of show.sets.entries()) {
        const setRef = collection(db, 'sets')
        try {
          const setDoc = await addDoc(setRef, {
            name: `Set ${index + 1}`,
            songs: set.songs.map((song, songIndex) => {
              const songId = songRefs.get(song.id)
              if (!songId)
                throw new Error(`Song reference not found for ${song.title}`)
              return {
                songId: doc(db, 'songs', songId),
                order: songIndex + 1,
                isLastSong: song.isLastSong || false
              }
            }),
            createdBy: user.value.uid,
            createdAt: show.createdAt
              ? Timestamp.fromDate(new Date(show.createdAt))
              : serverTimestamp(),
            updatedAt: show.updatedAt
              ? Timestamp.fromDate(new Date(show.updatedAt))
              : serverTimestamp()
          })
          setRefs.set(set.id, setDoc.id)
          createdDocs.push({ collection: 'sets', id: setDoc.id })
        } catch (error: unknown) {
          console.error('Failed to create set:', error)
          const errorMessage =
            error instanceof Error ? error.message : 'Failed to create set'
          return { success: false, error: errorMessage }
        }
      }

      // 3. Create setList
      const setListRef = collection(db, 'setLists')
      const setListDoc = await addDoc(setListRef, {
        name: show.title ? show.title : 'Untitled',
        sets: Array.from(setRefs.values()).map((setId, index) => ({
          setId: doc(db, 'sets', setId),
          order: index + 1
        })),
        lastUsed: serverTimestamp(),
        createdBy: user.value.uid,
        createdAt: show.createdAt
          ? Timestamp.fromDate(new Date(show.createdAt))
          : serverTimestamp(),
        updatedAt: show.updatedAt
          ? Timestamp.fromDate(new Date(show.updatedAt))
          : serverTimestamp()
      })
      createdDocs.push({ collection: 'setLists', id: setListDoc.id })

      // 4. Create the show
      const showRef = collection(db, 'shows')
      const showDoc = await addDoc(showRef, {
        title: show.title ? show.title : 'Untitled',
        venue: show.venue ? show.venue : 'No Venue',
        act: show.act ? show.act : 'No Act',
        date: show.date
          ? Timestamp.fromDate(new Date(show.date))
          : serverTimestamp(),
        setListId: doc(db, 'setLists', setListDoc.id),
        notes: show.notes || '',
        createdBy: user.value.uid,
        createdAt: show.createdAt
          ? Timestamp.fromDate(new Date(show.createdAt))
          : serverTimestamp(),
        updatedAt: show.updatedAt
          ? Timestamp.fromDate(new Date(show.updatedAt))
          : serverTimestamp()
      })
      createdDocs.push({ collection: 'shows', id: showDoc.id })

      // Update local copy
      const index = shows.value.findIndex(s => s.id === show.id)
      if (index !== -1) {
        shows.value[index] = {
          ...show,
          storageLocation: 'cloud'
        }
        localStorage.setItem(STORAGE_KEY, JSON.stringify(shows.value))
      }

      return { success: true }
    } catch (error: unknown) {
      console.error('Failed to migrate show to cloud:', error)
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to migrate show to cloud'
      // TODO: Implement rollback for created documents
      return {
        success: false,
        error: errorMessage
      }
    }
  }

  async function fetchCloudShows(): Promise<StoredShow[]> {
    if (!user.value) return []

    try {
      // 1. Fetch all shows for the user
      const showsRef = collection(db, 'shows')
      const showsQuery = query(
        showsRef,
        where('createdBy', '==', user.value.uid)
      )
      const showsSnapshot = await getDocs(showsQuery)

      if (showsSnapshot.empty) {
        return []
      }

      // 2. Fetch all related data in parallel
      const shows = await Promise.all(
        showsSnapshot.docs.map(async showDoc => {
          const showData = showDoc.data() as FirebaseShow

          // Fetch setList
          const setListDoc = await getDoc(showData.setListId)
          if (!setListDoc.exists())
            throw new Error(`SetList not found for show ${showDoc.id}`)
          const setListData = setListDoc.data() as FirebaseSetList

          // Fetch all sets in parallel
          const sets = await Promise.all(
            setListData.sets.map(async setRef => {
              const setDoc = await getDoc(setRef.setId)
              if (!setDoc.exists())
                throw new Error(`Set not found: ${setRef.setId.id}`)
              const setData = setDoc.data() as FirebaseSet

              // Fetch all songs in parallel
              const songs = await Promise.all(
                setData.songs.map(async songRef => {
                  const songDoc = await getDoc(songRef.songId)
                  if (!songDoc.exists())
                    throw new Error(`Song not found: ${songRef.songId.id}`)
                  const songData = songDoc.data() as FirebaseSong

                  return {
                    id: songDoc.id,
                    title: songData.title,
                    artist: songData.artist || '',
                    duration: songData.duration,
                    tempo: songData.tempo,
                    key: songData.key,
                    notes: songData.notes || '',
                    isLastSong: songRef.isLastSong || false,
                    lastPerformed: songData.lastPerformed,
                    timesPerformed: songData.timesPerformed || 0
                  }
                })
              )

              // Sort songs by order
              songs.sort((a, b) => {
                const aOrder =
                  setData.songs.find((s: any) => s.songId.id === a.id)?.order ||
                  0
                const bOrder =
                  setData.songs.find((s: any) => s.songId.id === b.id)?.order ||
                  0
                return aOrder - bOrder
              })

              return {
                id: setDoc.id,
                name: setData.name,
                songs
              }
            })
          )

          // Sort sets by order
          sets.sort((a, b) => {
            const aOrder =
              setListData.sets.find((s: any) => s.setId.id === a.id)?.order || 0
            const bOrder =
              setListData.sets.find((s: any) => s.setId.id === b.id)?.order || 0
            return aOrder - bOrder
          })

          // Construct the complete show object
          return {
            id: showDoc.id,
            title: showData.title,
            venue: showData.venue,
            act: showData.act,
            date:
              showData.date instanceof Timestamp
                ? showData.date.toDate().toISOString()
                : showData.date,
            sets,
            notes: showData.notes || '',
            storageLocation: 'cloud' as const,
            createdAt:
              showData.createdAt instanceof Timestamp
                ? showData.createdAt.toDate().toISOString()
                : showData.createdAt,
            updatedAt:
              showData.updatedAt instanceof Timestamp
                ? showData.updatedAt.toDate().toISOString()
                : showData.updatedAt
          }
        })
      )

      return shows
    } catch (error) {
      console.error('Failed to fetch cloud shows:', error)
      return []
    }
  }

  async function syncCloudShows() {
    if (!user.value) return

    try {
      // Fetch complete show data from cloud
      const cloudShows = await fetchCloudShows()
      if (cloudShows.length === 0) return

      // Create a map for quick lookup
      const cloudShowsMap = new Map(cloudShows.map(show => [show.id, show]))

      // Get set of local cloud show IDs for quick lookup
      const localCloudShowIds = new Set(
        shows.value
          .filter(show => show.storageLocation === 'cloud')
          .map(show => show.id)
      )

      // Update existing shows and add new ones from cloud
      const updatedShows = [
        // Keep all local shows that aren't cloud-stored
        ...shows.value.filter(show => show.storageLocation !== 'cloud'),

        // Update existing cloud shows
        ...shows.value
          .filter(show => show.storageLocation === 'cloud')
          .map(show => {
            const cloudShow = cloudShowsMap.get(show.id)
            if (!cloudShow) {
              // Show no longer exists in cloud, remove from local storage
              return null
            }

            // Update local show with cloud data if it's newer
            if (show.updatedAt < cloudShow.updatedAt) {
              return cloudShow
            }

            return show
          })
          .filter((show): show is StoredShow => show !== null),

        // Add new shows from cloud that don't exist locally
        ...cloudShows.filter(show => !localCloudShowIds.has(show.id))
      ]

      shows.value = updatedShows
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedShows))
    } catch (error) {
      console.error('Failed to sync cloud shows:', error)
    }
  }

  watch(user, async newUser => {
    if (newUser) {
      await syncCloudShows()
    }
  })

  onMounted(() => {
    shows.value = getAllShows()
  })

  return {
    shows,
    currentShow,
    saveShow,
    updateShow,
    deleteShow,
    loadShow,
    migrateToCloud,
    syncCloudShows,
    fetchCloudShows
  }
}
