import { ref } from 'vue'

interface SaveOperation {
  id: string
  save: () => Promise<void>
}

export function useDebouncedSave(delay = 2000) {
  const isSaving = ref<Record<string, boolean>>({})
  const saveTimeouts = new Map<string, NodeJS.Timeout>()
  const pendingSaves = new Map<string, SaveOperation>()

  const debouncedSave = async (id: string, saveOperation: () => Promise<void>) => {
    isSaving.value[id] = true
    
    // Clear any existing timeout for this id
    if (saveTimeouts.has(id)) {
      clearTimeout(saveTimeouts.get(id))
    }

    // Store the save operation
    pendingSaves.set(id, { id, save: saveOperation })

    // Create new timeout
    const timeout = setTimeout(async () => {
      const operation = pendingSaves.get(id)
      if (operation) {
        try {
          await operation.save()
        } catch (error) {
          console.error('Failed to save:', error)
        } finally {
          isSaving.value[id] = false
          pendingSaves.delete(id)
          saveTimeouts.delete(id)
        }
      }
    }, delay)

    saveTimeouts.set(id, timeout)
  }

  // Force save all pending changes
  const saveAll = async () => {
    // Clear all timeouts
    for (const timeout of saveTimeouts.values()) {
      clearTimeout(timeout)
    }
    saveTimeouts.clear()

    // Execute all pending saves
    const saves = Array.from(pendingSaves.values())
    pendingSaves.clear()

    await Promise.all(saves.map(async ({ id, save }) => {
      try {
        isSaving.value[id] = true
        await save()
      } catch (error) {
        console.error('Failed to save:', error)
      } finally {
        isSaving.value[id] = false
      }
    }))
  }

  return {
    isSaving,
    debouncedSave,
    saveAll
  }
}
