import { ref } from 'vue'
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  updateProfile,
  type User
} from 'firebase/auth'
import { auth } from '@/firebase'

export function useAuth() {
  const user = ref<User | null>(null)
  const error = ref<string | null>(null)
  const loading = ref(false)
  const showMigrationPrompt = ref(false)

  // Initialize the auth state
  onAuthStateChanged(auth, _user => {
    user.value = _user
  })

  const login = async (email: string, password: string) => {
    error.value = null
    loading.value = true
    try {
      const { user: userData } = await signInWithEmailAndPassword(
        auth,
        email,
        password
      )
      user.value = userData
    } catch (err: any) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const register = async (email: string, password: string, name: string) => {
    error.value = null
    loading.value = true
    try {
      const { user: userData } = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      )
      await updateProfile(userData, { displayName: name })
      user.value = userData
    } catch (err: any) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    error.value = null
    try {
      await signOut(auth)
      user.value = null
    } catch (err: any) {
      error.value = err.message
    }
  }

  return {
    user,
    error,
    loading,
    login,
    register,
    logout,
    showMigrationPrompt
  }
}
