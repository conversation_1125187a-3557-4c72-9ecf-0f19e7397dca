import type {
  FirebaseUser,
  FirebaseShow,
  FirebaseSetList,
  FirebaseSet,
  FirebaseSong,
  FirebaseSetListItem,
  FirebaseSetSongItem
} from '@/types/firebase'
import { Timestamp } from 'firebase/firestore'

// Generic validation result type
interface ValidationResult {
  isValid: boolean
  errors: string[]
}

// Timestamp validation
function isValidTimestamp(timestamp: any): boolean {
  return timestamp instanceof Timestamp && timestamp <= Timestamp.now()
}

// Base document validation
function validateBaseFields(data: any): string[] {
  const errors: string[] = []

  if (!data.createdBy || typeof data.createdBy !== 'string') {
    errors.push('createdBy must be a valid user reference')
  }
  if (!isValidTimestamp(data.createdAt)) {
    errors.push('createdAt must be a valid timestamp')
  }
  if (!isValidTimestamp(data.updatedAt)) {
    errors.push('updatedAt must be a valid timestamp')
  }

  return errors
}

// User validation
export function validateUser(data: Partial<FirebaseUser>): ValidationResult {
  const errors = validateBaseFields(data)

  if (!data.email || typeof data.email !== 'string') {
    errors.push('email is required and must be a string')
  }
  if (!data.uid || typeof data.uid !== 'string') {
    errors.push('uid is required and must be a string')
  }
  if (!data.displayName || typeof data.displayName !== 'string') {
    errors.push('displayName is required and must be a string')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Show validation
export function validateShow(data: Partial<FirebaseShow>): ValidationResult {
  const errors = validateBaseFields(data)

  if (!data.title || typeof data.title !== 'string') {
    errors.push('title is required and must be a string')
  }
  if (!data.venue || typeof data.venue !== 'string') {
    errors.push('venue is required and must be a string')
  }
  if (!data.act || typeof data.act !== 'string') {
    errors.push('act is required and must be a string')
  }
  if (!isValidTimestamp(data.date)) {
    errors.push('date must be a valid timestamp')
  }
  if (!data.setListId || typeof data.setListId !== 'string') {
    errors.push('setListId must be a valid setList reference')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// SetList validation
export function validateSetList(
  data: Partial<FirebaseSetList>
): ValidationResult {
  const errors = validateBaseFields(data)

  if (!data.name || typeof data.name !== 'string') {
    errors.push('name is required and must be a string')
  }

  if (!Array.isArray(data.sets)) {
    errors.push('sets must be an array')
  } else {
    if (data.sets.length > 4) {
      errors.push('setList cannot contain more than 4 sets')
    }

    data.sets.forEach((set: FirebaseSetListItem, index: number) => {
      if (!set.setId || typeof set.setId !== 'string') {
        errors.push(`set ${index + 1}: setId must be a valid set reference`)
      }
      if (!set.order || set.order < 1 || set.order > 4) {
        errors.push(`set ${index + 1}: order must be between 1 and 4`)
      }
    })
  }

  if (!isValidTimestamp(data.lastUsed)) {
    errors.push('lastUsed must be a valid timestamp')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Set validation
export function validateSet(data: Partial<FirebaseSet>): ValidationResult {
  const errors = validateBaseFields(data)

  if (!data.name || typeof data.name !== 'string') {
    errors.push('name is required and must be a string')
  }

  if (!Array.isArray(data.songs)) {
    errors.push('songs must be an array')
  } else {
    if (data.songs.length < 1) {
      errors.push('set must contain at least one song')
    }

    data.songs.forEach((song: FirebaseSetSongItem, index: number) => {
      if (!song.songId || typeof song.songId !== 'string') {
        errors.push(`song ${index + 1}: songId must be a valid song reference`)
      }
      if (typeof song.order !== 'number') {
        errors.push(`song ${index + 1}: order must be a number`)
      }
      if (typeof song.isLastSong !== 'boolean') {
        errors.push(`song ${index + 1}: isLastSong must be a boolean`)
      }
      if (typeof song.isEncoreSeparator !== 'boolean') {
        errors.push(`song ${index + 1}: isEncoreSeparator must be a boolean`)
      }
    })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Song validation
export function validateSong(data: Partial<FirebaseSong>): ValidationResult {
  const errors = validateBaseFields(data)

  if (!data.title || typeof data.title !== 'string') {
    errors.push('title is required and must be a string')
  }
  if (!data.key || typeof data.key !== 'string') {
    errors.push('key is required and must be a string')
  }

  // Optional fields validation
  if (data.artist && typeof data.artist !== 'string') {
    errors.push('artist must be a string')
  }
  if (data.duration && typeof data.duration !== 'number') {
    errors.push('duration must be a number')
  }
  if (data.tempo && typeof data.tempo !== 'number') {
    errors.push('tempo must be a number')
  }
  if (data.notes && typeof data.notes !== 'string') {
    errors.push('notes must be a string')
  }
  if (data.lastPerformed && !isValidTimestamp(data.lastPerformed)) {
    errors.push('lastPerformed must be a valid timestamp')
  }
  if (data.timesPerformed && typeof data.timesPerformed !== 'number') {
    errors.push('timesPerformed must be a number')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Composite validation helper
export function validateDocument(
  collectionName: string,
  data: any
): ValidationResult {
  switch (collectionName) {
    case 'users':
      return validateUser(data)
    case 'shows':
      return validateShow(data)
    case 'setLists':
      return validateSetList(data)
    case 'sets':
      return validateSet(data)
    case 'songs':
      return validateSong(data)
    default:
      return {
        isValid: false,
        errors: [`Unknown collection: ${collectionName}`]
      }
  }
}
