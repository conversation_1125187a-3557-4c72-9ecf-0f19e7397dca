declare module 'jspdf' {
  class jsPDF {
    constructor(options?: {
      orientation?: 'p' | 'portrait' | 'l' | 'landscape'
      unit?: 'pt' | 'px' | 'in' | 'mm' | 'cm' | 'ex' | 'em' | 'pc'
      format?: string | [number, number]
      compress?: boolean
      precision?: number
      filters?: string[]
    })

    // Page operations
    addPage(
      format?: string | [number, number],
      orientation?: 'p' | 'portrait' | 'l' | 'landscape'
    ): jsPDF

    // Text operations
    setFontSize(size: number): jsPDF
    setFont(fontName: string, fontStyle?: string): jsPDF
    text(
      text: string | string[],
      x: number,
      y: number,
      options?: {
        align?: 'left' | 'center' | 'right' | 'justify'
        baseline?: 'alphabetic' | 'ideographic' | 'bottom' | 'top' | 'middle'
        angle?: number
        rotationDirection?: 0 | 1
        charSpace?: number
        lineHeightFactor?: number
        maxWidth?: number
        renderingMode?: string
      }
    ): jsPDF

    // Graphics operations
    line(x1: number, y1: number, x2: number, y2: number): jsPDF
    rect(
      x: number,
      y: number,
      w: number,
      h: number,
      style?: 'F' | 'S' | 'DF' | 'FD'
    ): jsPDF

    // Image operations
    addImage(
      imageData: string | HTMLImageElement | HTMLCanvasElement | Uint8Array,
      format: 'JPEG' | 'PNG' | 'WEBP',
      x: number,
      y: number,
      width: number,
      height: number,
      alias?: string,
      compression?: 'NONE' | 'FAST' | 'MEDIUM' | 'SLOW',
      rotation?: number
    ): jsPDF

    // Internal properties
    internal: {
      scaleFactor: number
      pageSize: {
        width: number
        height: number
        getWidth(): number
        getHeight(): number
      }
      getFont(): any
      getLineWidth(): number
    }

    // Measurement and output
    getStringUnitWidth(text: string): number
    getFontSize(): number
    output(
      type:
        | 'arraybuffer'
        | 'blob'
        | 'bloburi'
        | 'bloburl'
        | 'datauristring'
        | 'dataurlstring'
        | 'datauri'
        | 'dataurl'
        | 'raw',
      options?: any
    ): string | Uint8Array | Blob
    save(filename: string): void
  }

  // Export the class as the default export
  const jsPDF: typeof jsPDF
  export default jsPDF
}
