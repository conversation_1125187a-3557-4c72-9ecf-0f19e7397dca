declare module '@react-pdf/renderer' {
  import { Component } from 'vue'

  export interface Style {
    [key: string]: string | number | undefined
  }

  export interface DocumentProps {
    title?: string
    author?: string
    subject?: string
    keywords?: string
    creator?: string
    producer?: string
  }

  export interface PageProps {
    size?: string | [number, number]
    orientation?: 'portrait' | 'landscape'
    style?: Style
  }

  export interface TextProps {
    style?: Style
    render?: (props: any) => React.ReactNode
  }

  export interface ViewProps {
    style?: Style
    wrap?: boolean
  }

  export const Document: Component<DocumentProps>
  export const Page: Component<PageProps>
  export const Text: Component<TextProps>
  export const View: Component<ViewProps>
  export const StyleSheet: {
    create: <T extends Record<string, Style>>(styles: T) => T
  }
}
