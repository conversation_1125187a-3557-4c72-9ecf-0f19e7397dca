declare module 'vuedraggable' {
  import { DefineComponent } from 'vue'

  export interface DraggableOptions {
    animation?: number
    disabled?: boolean
    ghostClass?: string
    group?:
      | string
      | {
          name: string
          pull?: boolean | 'clone' | Function
          put?: boolean | string[] | Function
        }
    handle?: string
    itemKey?: string
    modelValue?: any[]
    move?: Function
    componentData?: object
  }

  const draggable: DefineComponent<DraggableOptions>
  export default draggable
}
