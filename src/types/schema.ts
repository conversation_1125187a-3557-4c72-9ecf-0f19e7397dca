import type {
  FirebaseUser,
  FirebaseShow,
  FirebaseSetList,
  FirebaseSet,
  FirebaseSong
} from './firebase'

export interface SchemaRules {
  setLists: {
    maxSets: number
  }
  sets: {
    minSongs: number
  }
}

export interface Schema {
  collections: {
    users: { fields: Record<keyof FirebaseUser, any> }
    shows: { fields: Record<keyof FirebaseShow, any> }
    setLists: { fields: Record<keyof FirebaseSetList, any> }
    sets: { fields: Record<keyof FirebaseSet, any> }
    songs: { fields: Record<keyof FirebaseSong, any> }
  }
  version: string
  description: string
  rules: SchemaRules
}
