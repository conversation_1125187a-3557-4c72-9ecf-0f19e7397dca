import type { Timestamp } from 'firebase/firestore'
import type { BaseDocument } from './index'

// Firebase-specific interfaces
export interface FirebaseUser extends BaseDocument {
  email: string
  uid: string
  displayName: string
}

export interface FirebaseShow extends BaseDocument {
  title: string
  venue: string
  act: string
  date: Timestamp
  setListId: string // Reference to setLists collection
}

export interface FirebaseSetListItem {
  setId: string
  order: number
}

export interface FirebaseSetList extends BaseDocument {
  name: string
  sets: FirebaseSetListItem[]
  lastUsed: Timestamp
}

export interface FirebaseSetSongItem {
  songId: string
  order: number
  isLastSong: boolean
}

export interface FirebaseSong extends BaseDocument {
  title: string
  artist: string
  duration: number
  key: string
  notes: string
  tempo: number
  lastPerformed: Timestamp
  timesPerformed: number
}

export interface FirebaseSet extends BaseDocument {
  name: string
  songs: FirebaseSetSongItem[]
}
