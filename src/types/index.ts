import type { Timestamp } from 'firebase/firestore'

// Basic types
export type StorageLocation = 'local' | 'cloud'

// Base interfaces
export interface BaseEntity {
  id: string
  createdAt?: string
  updatedAt?: string
}

// Core interfaces
export interface Song extends BaseEntity {
  title: string
  shortTitle?: string
  artist?: string
  durationSecs?: number
  bpm?: number
  key: string
  notes?: string
  isLastSong?: boolean
  isEncoreSeparator?: boolean
  lastPerformed?: Timestamp | string
  timesPerformed?: number
  tags?: string[]
  year?: number
  composers?: string[]
}

export interface Set extends BaseEntity {
  title: string
  songs: Song[]
  order?: number
}

export interface SetList extends BaseEntity {
  name?: string
  sets: Set[]
  hasEncore?: boolean
}

export interface Show extends BaseEntity {
  title: string
  venue: string
  act: string
  date: string
  sets: Set[]
  notes?: string
  storageLocation: StorageLocation
}

export interface Venue extends BaseEntity {
  name: string
  address?: string
  capacity?: number
  contactName?: string
  contactEmail?: string
  contactPhone?: string
  notes?: string
}

export interface Artist extends BaseEntity {
  name: string
  email?: string
  phone?: string
  notes?: string
}

export interface Act extends BaseEntity {
  name: string
  artists: Artist[]
  notes?: string
}

export type StoredShow = Show & {
  id: string
  createdAt: string
  updatedAt: string
}

// Collection references type
export type CollectionName = 'users' | 'shows' | 'setLists' | 'sets' | 'songs'
