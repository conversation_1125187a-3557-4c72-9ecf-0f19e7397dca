:root {
  /* Colors */
  --color-primary: #3b82f6;
  --color-primary-light: #60a5fa;
  --color-primary-dark: #2563eb;
  --color-primary-muted: #93c5fd;
  --color-primary-alpha: rgb(59 130 246 / 0.7);

  --color-secondary-hue: 180;
  --color-secondary: hsl(var(--color-secondary-hue) 30% 40%);
  --color-secondary-light: hsl(var(--color-secondary-hue) 30% 80%);
  --color-secondary-dark: hsl(var(--color-secondary-hue) 30% 25%);
  --color-secondary-muted: hsl(var(--color-secondary-hue) 15% 80%);
  --color-secondary-alpha: hsl(var(--color-secondary-hue) 30% 40% / 0.7);

  /* Surface Colors */
  --color-background: #f8f8f8;
  --color-surface: #ffffff;
  --color-surface-light: #f9fafb;
  --color-surface-dark: #374151;
  --color-surface-hover: #37415120;
  --color-surface-muted: #f3f4f6;
  --color-surface-alpha: rgb(255 255 255 / 0.7);

  /* Primary color in RGB format for animations */
  --color-primary-rgb: 59, 130, 246;

  /* Text Colors */
  --color-text: #1e293b;
  --color-text-light: #64748b;
  --color-text-muted: #9ca3af;
  --color-text-dark: #1e293b;
  --color-text-white: var(--color-background);
  --color-text-alpha: rgb(30 41 59 / 0.7);

  /* Button Colors */
  --color-button: #3b82f6;
  --color-button-light: #60a5fa;
  --color-button-dark: #2563eb;
  --color-button-muted: #93c5fd;
  --color-button-alpha: rgb(59 130 246 / 0.7);

  /* Error Colors */
  --color-error: #dc2626;
  --color-error-light: #fee2e2;
  --color-error-dark: #991b1b;
  --color-error-muted: #fca5a5;
  --color-error-alpha: rgb(220 38 38 / 0.7);

  /* Success Colors */
  --color-success: #16a34a;
  --color-success-light: #dcfce7;
  --color-success-dark: #047857;
  --color-success-muted: #86efac;
  --color-success-alpha: rgb(22 163 74 / 0.7);

  /* Info Colors */
  --color-info: #0ea5e9;
  --color-info-light: #e0f2fe;
  --color-info-dark: #0369a1;
  --color-info-muted: #7dd3fc;
  --color-info-alpha: rgb(14 165 233 / 0.7);

  /* Warning Colors */
  --color-warning: #f59e0b;
  --color-warning-light: #fef3c7;
  --color-warning-dark: #b45309;
  --color-warning-muted: #fcd34d;
  --color-warning-alpha: rgb(245 158 11 / 0.7);

  /* Accent Colors */
  --color-accent: #8b5cf6;
  --color-accent-light: #ddd6fe;
  --color-accent-dark: #6d28d9;
  --color-accent-muted: #c4b5fd;
  --color-accent-alpha: rgb(139 92 246 / 0.7);

  /* Spacing */
  --space-2xs: clamp(0.125rem, 0.25vw, 0.25rem);
  --space-xs: clamp(0.25rem, 0.5vw, 0.5rem);
  --space-sm: clamp(0.5rem, 1vw, 1rem);
  --space-md: clamp(1rem, 2vw, 2rem);
  --space-lg: clamp(1.5rem, 3vw, 3rem);
  --space-xl: clamp(2rem, 4vw, 4rem);
  --space-2xl: clamp(2.5rem, 5vw, 5rem);

  /* Typography */
  --font-size-2xs: clamp(0.625rem, 1.25vw, 0.75rem);
  --font-size-xs: clamp(0.75rem, 1.5vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 1.75vw, 1rem);
  --font-size-base: clamp(1rem, 2vw, 1.125rem);
  --font-size-md: clamp(1rem, 2vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 2.25vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 2.5vw, 1.5rem);
  --font-size-2xl: clamp(1.5rem, 3vw, 2rem);

  /* Border Radius */
  --roundness: 1;
  --radius-unit: 0.25rem;
  --radius-sm: calc(var(--radius-unit) * 1.5 * var(--roundness));
  --radius-md: calc(var(--radius-unit) * 2 * var(--roundness));
  --radius-lg: calc(var(--radius-unit) * 3 * var(--roundness));
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.25);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.25);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.25);
  --bs-neu: -8px -8px 8px rgb(255 255 255 / 1), 8px 8px 8px rgb(0 0 0 / 0.02),
    -1px -1px 1px white, 1px 1px 1px rgba(0, 0, 0, 0.1);
  --shadow-hover-sm: 0 3px 3px 0 rgb(0 0 0 / 0.17);
  --shadow-hover-md: 0 8px 8px -3px rgb(0 0 0 / 0.17);
  --shadow-hover-lg: 0 12px 16px -4px rgb(0 0 0 / 0.27);

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;

  /* Time Display */
  --color-duration: var(--color-text-light);
  --color-duration-warning: var(--color-warning);
  --color-duration-error: var(--color-error);

  /* Duration Thresholds (in minutes) */
  --duration-set-min: 20;
  --duration-set-max: 45;
  --duration-show-standard: 90;
  --duration-song-default: 3.5;
}
