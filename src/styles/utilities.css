/* Layout */
.container {
  width: min(100% - 2rem, 80rem);
  margin-inline: auto;
}

.flex {
  display: flex;
  gap: var(--space-sm);
}

.grid {
  display: grid;
  gap: var(--space-sm);
}

/* Components */
.button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  border: none;
  background-color: var(--color-accent);
  color: var(--color-text-white);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.button:hover {
  background-color: var(--color-accent-dark);
}

.button:focus-visible {
  outline: 2px solid var(--color-accent-muted);
  outline-offset: 2px;
}

.input {
  padding: var(--space-sm);
  border: 1px solid var(--color-text-muted);
  border-radius: var(--radius-md);
  background-color: var(--color-surface);
  font-size: var(--font-size-sm);
  transition: border-color var(--transition-fast);
}

.input:focus {
  outline: none;
  border-color: var(--color-accent);
}

/* Spacing */
.gap-sm {
  gap: var(--space-sm);
}
.gap-md {
  gap: var(--space-md);
}
.gap-lg {
  gap: var(--space-lg);
}

.p-sm {
  padding: var(--space-sm);
}
.p-md {
  padding: var(--space-md);
}
.p-lg {
  padding: var(--space-lg);
}

.m-sm {
  margin: var(--space-sm);
}
.m-md {
  margin: var(--space-md);
}
.m-lg {
  margin: var(--space-lg);
}

/* Typography */
.text-sm {
  font-size: var(--font-size-sm);
}
.text-md {
  font-size: var(--font-size-md);
}
.text-lg {
  font-size: var(--font-size-lg);
}
.text-xl {
  font-size: var(--font-size-xl);
}

.text-light {
  color: var(--color-text-light);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn var(--transition-normal);
}
