@import './reset.css';
@import './variables.css';
@import './utilities.css';

a {
  color: var(--color-primary);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

a:visited {
  color: var(--color-primary-light);
}

a:active {
  color: var(--color-primary-dark);
}

/* App-specific styles */
.app {
  min-height: 100vh;
  background: var(--color-background);
}

/* Set List Specific Styles */
.set-list {
  display: grid;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.set {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  box-shadow: var(--shadow-md);
}

/* 
.song-item__control {
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.song-item:nth-child(even) {
  background: var(--color-surface);
}

.song-item:hover:not(:has(:focus-within)) {
  outline: 1px solid var(--color-accent);
}

.song-item:hover .song-item__control,
.song-item:focus-within .song-item__control {
  opacity: 1;
}

.song-item.dragging {
  opacity: 0.5;
  background-color: var(--color-accent-muted);
} */

/* Common Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-md);
}

.main {
  padding: 2rem 0;
  transition: padding-right 0.3s ease;
}

.main.preview-open {
  padding-right: 600px;
}

/* PDF Preview Panel */
.pdf-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 600px;
  height: 100vh;
  background: var(--color-surface);
  box-shadow: var(--shadow-lg);
  z-index: 10;
  animation: slideIn var(--transition-normal) ease-out;
}

/* Form Styles */
.form-input {
  flex-grow: 1;
}

.form-input label {
  margin-inline-start: var(--space-xs);
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
}

.input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
}

/* Transitions */
.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.slide-enter-to,
.slide-leave-from {
  transform: translateX(0);
  opacity: 1;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}
