<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { doc, onSnapshot } from 'firebase/firestore'
import { db } from '@/firebase'
import { Song } from '@/classes/Song'

const route = useRoute()
const router = useRouter()
const song = ref<Song | null>(null)
const isEditing = ref(false)
const editedSong = ref<Partial<Song>>({})
let unsubscribe: () => void

// Computed property for composers string
const composersString = computed({
  get: () => editedSong.value.composers?.join(', ') || '',
  set: (value: string) => {
    // Clean and validate the composers
    const cleanedComposers = value
      .split(',')
      .map(composer => composer.trim())
      .filter(composer => composer.length > 0) // Only keep non-empty values

    // Only update if we have valid composers
    if (cleanedComposers.length > 0) {
      editedSong.value.composers = cleanedComposers
    } else {
      editedSong.value.composers = []
    }
  }
})

// Subscribe to song updates
onMounted(() => {
  const songId = route.params.id as string
  const songRef = doc(db, 'songs', songId)


  unsubscribe = onSnapshot(songRef, (doc) => {
    if (doc.exists()) {
      song.value = Song.fromFirestore(doc.data(), doc.id)
      if (route.query.edit === 'true') {
        startEdit()
      }
    } else {
      router.push('/songs')
    }
  })
})

onUnmounted(() => {
  unsubscribe()
})

const startEdit = () => {
  editedSong.value = {
    title: song.value?.title,
    shortTitle: song.value?.shortTitle,
    key: song.value?.key,
    artist: song.value?.artist,
    bpm: song.value?.bpm,
    notes: song.value?.notes,
    durationSecs: song.value?.durationSecs,
    year: song.value?.year,
    composers: song.value?.composers
  }
  isEditing.value = true
}

const cancelEdit = () => {
  isEditing.value = false
  editedSong.value = {}
}

const saveEdit = async () => {
  if (!song.value) return

  // Convert year to number or remove it from object
  if (editedSong.value.year) {
    editedSong.value.year = Number(editedSong.value.year)
  } else {
    delete editedSong.value.year
  }

  // Ensure composers is clean before saving
  if (editedSong.value.composers) {
    editedSong.value.composers = editedSong.value.composers
      .map(composer => composer.trim())
      .filter(composer => composer.length > 0)
  }

  // Remove empty short title
  if (!!editedSong.value.shortTitle) {
    delete editedSong.value.shortTitle
  }

  Object.assign(song.value, editedSong.value)
  await song.value.update()
  isEditing.value = false

  if (route.query.edit === 'true') {
    router.push({ name: 'songs' })
  }
}

const handleDelete = async () => {
  if (!song.value) return

  if (confirm('Are you sure you want to delete this song?')) {
    await song.value.delete()
    router.push('/songs')
  }
}

const goBack = () => {
  router.push('/songs')
}
</script>

<template>
  <div v-if="song" class="song-page">
    <header class="page-header">
      <BaseButton @click="goBack" variant="secondary" size="compact">← Back to Songs</BaseButton>
      <div class="actions">
        <BaseButton v-if="!isEditing" @click="startEdit" variant="secondary">Edit</BaseButton>
        <BaseButton @click="handleDelete" variant="danger">Delete</BaseButton>
      </div>
    </header>

    <main class="song-content">
      <template v-if="isEditing">
        <div class="edit-form">
          <div class="form-row">
            <div class="form-group">
              <label for="title">Title</label>
              <input id="title" v-model="editedSong.title" type="text" required>
            </div>
            <div class="form-group">
              <label for="shortTitle">Short Title</label>
              <input id="shortTitle" v-model="editedSong.shortTitle" type="text">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="key">Key</label>
              <input id="key" v-model="editedSong.key" type="text">
            </div>
            <div class="form-group">
              <label for="artist">Artist</label>
              <input id="artist" v-model="editedSong.artist" type="text">
            </div>
            <div class="form-group">
              <label for="year">Year</label>
              <input id="year" v-model="editedSong.year" type="number">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="composers">Composers (comma-separated)</label>
              <input id="composers" v-model="composersString" type="text"
                placeholder="e.g. John Lennon, Paul McCartney">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="bpm">BPM</label>
              <input id="bpm" v-model="editedSong.bpm" type="number">
            </div>
            <div class="form-group">
              <label for="duration">Duration (seconds)</label>
              <input id="duration" v-model="editedSong.durationSecs" type="number">
            </div>
          </div>

          <div class="form-group">
            <label for="notes">Notes</label>
            <textarea id="notes" v-model="editedSong.notes" rows="4"></textarea>
          </div>

          <div class="form-actions">
            <BaseButton @click="saveEdit" variant="primary">Save Changes</BaseButton>
            <BaseButton @click="cancelEdit" variant="secondary">Cancel</BaseButton>
          </div>
        </div>
      </template>

      <template v-else>
        <div class="song-header">
          <h1>{{ song.title }}</h1>
          <p v-if="song.shortTitle && song.shortTitle !== song.title" class="short-title">{{ song.shortTitle }}</p>
        </div>

        <div class="song-details">
          <div class="detail-group">
            <label>Key</label>
            <div class="key">{{ song.key }}</div>
          </div>

          <div v-if="song.artist" class="detail-group">
            <label>Artist</label>
            <div>{{ song.artist }}</div>
          </div>

          <div v-if="song.year" class="detail-group">
            <label>Year</label>
            <div>{{ song.year }}</div>
          </div>

          <div v-if="song.composers?.length" class="detail-group">
            <label>Composers</label>
            <div>{{ song.getComposersString() }}</div>
          </div>

          <div v-if="song.bpm" class="detail-group">
            <label>BPM</label>
            <div>{{ song.bpm }}</div>
          </div>

          <div v-if="song.durationSecs" class="detail-group">
            <label>Duration</label>
            <div>{{ song.duration() }}</div>
          </div>
        </div>

        <div v-if="song.notes" class="song-notes">
          <h2>Notes</h2>
          <p>{{ song.notes }}</p>
        </div>
      </template>
    </main>
  </div>
</template>

<style scoped>
.song-page {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-md);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-block-end: var(--space-lg);
}

.actions {
  display: flex;
  gap: var(--space-sm);
}

.song-content {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.song-header {
  margin-block-end: var(--space-lg);
}

.short-title {
  font-size: 1.1em;
  color: var(--color-text-muted);
  margin-block-start: var(--space-xs);
}

.song-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-md);
  margin-block-end: var(--space-xl);
}

.detail-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.detail-group label {
  font-size: 0.9em;
  color: var(--color-text-muted);
  font-weight: 500;
}

.key {
  display: inline-block;
  padding: var(--space-xs) var(--space-sm);
  background: var(--color-surface-accent);
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.song-notes {
  border-top: 1px solid var(--color-border);
  padding-block-start: var(--space-lg);
}

.song-notes h2 {
  font-size: 1.1em;
  margin-block-end: var(--space-sm);
}

/* Edit Form Styles */
.edit-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-sm);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.form-group label {
  font-size: 0.9em;
  color: var(--color-text-muted);
  font-weight: 500;
}

input,
textarea {
  padding: var(--space-xs);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-background);
  color: var(--color-text);
  font-size: 0.9em;
  width: 100%;
}

textarea {
  resize: vertical;
}

.form-actions {
  display: flex;
  gap: var(--space-sm);
  justify-content: flex-end;
  margin-block-start: var(--space-md);
}
</style>