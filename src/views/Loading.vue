<template>
  <div class="loading-container">
    <div class="loading-content">
      <div class="logo-container">
        <Music class="logo-icon" />
      </div>
      <div class="loading-text">Loading</div>
      <div class="loading-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Music } from 'lucide-vue-next'
</script>

<style scoped>
.loading-container {
  min-height: calc(100vh - var(--header-height));
  display: grid;
  place-items: center;
  background: var(--color-background);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-lg);
}

.logo-container {
  width: 80px;
  height: 80px;
  background: var(--color-primary);
  border-radius: 50%;
  display: grid;
  place-items: center;
  animation: pulse 2s infinite;
}

.logo-icon {
  width: 40px;
  height: 40px;
  color: white;
}

.loading-text {
  font-size: var(--font-size-xl);
  font-weight: 500;
  color: var(--color-text);
}

.loading-dots {
  display: flex;
  gap: var(--space-sm);
}

.loading-dots span {
  width: 8px;
  height: 8px;
  background-color: var(--color-primary);
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 20px rgba(var(--color-primary-rgb), 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0);
  }
}

@keyframes bounce {
  0%, 80%, 100% { 
    transform: scale(0);
  }
  40% { 
    transform: scale(1);
  }
}
</style>
