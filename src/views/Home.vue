<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Music, ListMusic, Cloud, Eye } from 'lucide-vue-next'
import { useShowStorage } from '@/composables/useShowStorage'
import '@/assets/hero-image.webp'
import '@/assets/hero-image.jpg'

const router = useRouter()
const { shows } = useShowStorage()
const size = 64;

const navigateToShows = () => {
  router.push({ name: 'shows' })
}

const navigateToNewShow = () => {
  router.push({ name: 'new-show' })
}
</script>

<template>
  <div class="home">
    <div class="hero">
      <div class="hero-content">
        <Music :size="64" class="hero-icon" />
        <h1>Set List Creator</h1>
        <p class="tagline">Create, manage, and share your set lists with ease</p>
        <div class="cta-buttons">
          <BaseButton @click="navigateToNewShow" shadow variant="primary" size="large">
            <ListMusic class="button-icon" />
            Create {{ shows.length ? 'New' : 'Your First' }} Show
          </BaseButton>
          <BaseButton v-if="shows.length" @click="navigateToShows" shadow variant="secondary" size="large">
            <Eye class="button-icon" />
            View Your Shows
          </BaseButton>
        </div>
      </div>
    </div>

    <section class="features">
      <h2>Features</h2>
      <div class="features-grid">
        <BaseCard class="features-card">
          <ListMusic class="feature-icon" :size />
          <h3>Easy Set List Creation</h3>
          <p>Create and organize your shows, set lists and songs with an intuitive drag-and-drop interface</p>
        </BaseCard>
        <BaseCard class="features-card">
          <Cloud class="feature-icon" :size />
          <h3>Cloud Sync</h3>
          <p>Keep your set lists in sync across devices with secure cloud storage</p>
        </BaseCard>
        <BaseCard class="features-card">
          <Music class="feature-icon" :size />
          <h3>Show Management</h3>
          <p>Track venues, dates, and notes for all your shows in one place</p>
        </BaseCard>
      </div>
    </section>
  </div>
</template>

<style scoped>
.home {
  min-height: 100vh;
}

.hero {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: var(--space-xl) var(--space-md);
  background: linear-gradient(to bottom right, var(--color-primary-light), var(--color-primary-dark));
  background-image: linear-gradient(#b9a099, #eadeca, #ae9888);
  background-image: url('@/assets/hero-image.jpg');
  background-image: url('@/assets/hero-image.webp');
  background-size: cover;
  background-position: center;
}

.hero-content {
  text-align: center;
  max-width: 800px;
  padding: var(--space-lg);
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border-radius: var(--radius-lg);
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 0, 0, 0.1), 0 0 10px rgba(0, 0, 0, 0.05);
}

.hero-icon {
  box-sizing: content-box;
  margin-inline: auto;
  margin-bottom: var(--space-md);
  padding: 30px;
  border-radius: 50%;
  background-color: var(--color-primary);
  box-shadow: var(--shadow-md);
  color: white;
}

h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: var(--space-sm);
}

.tagline {
  font-size: 1.5rem;
  margin-bottom: var(--space-xl);
  opacity: 0.9;
  text-wrap: pretty;
}

.cta-buttons {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
}

.button-icon {
  margin-right: var(--space-xs);
}

.features {
  padding: var(--space-xl) var(--space-md);
  background: var(--color-surface);
}

.features h2 {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: var(--space-xl);
  color: var(--color-text);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  grid-template-rows: auto auto 1fr;
  gap: var(--space-md);
  max-width: 1200px;
  margin: 0 auto;
}

.features-card {
  display: grid;
  grid-template-rows: subgrid;
  grid-row: span 3;
  gap: var(--space-sm);
}

.feature-icon {
  color: var(--color-primary);
}

.feature-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text);
}

.feature-card p {
  color: var(--color-text-muted);
  line-height: 1.6;
}

@media (max-width: 768px) {
  .hero {
    min-height: 50vh;
  }

  h1 {
    font-size: 2.5rem;
  }

  .tagline {
    font-size: 1.25rem;
  }

  .cta-buttons {
    flex-direction: column;
  }
}
</style>