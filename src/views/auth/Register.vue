<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '@/composables/useAuth'
const router = useRouter()
const { register, error, loading } = useAuth()

const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const name = ref('')
const validationError = ref('')

const handleSubmit = async () => {
  validationError.value = ''

  if (password.value !== confirmPassword.value) {
    validationError.value = 'Passwords do not match'
    return
  }

  await register(email.value, password.value, name.value)
  if (!error.value) {
    router.push('/')
  }
}
</script>

<template>
  <div class="auth-form">
    <div class="auth-form-header">
      <h2>Register</h2>
      <small>* indicates required field</small>
    </div>

    <form @submit.prevent="handleSubmit">
      <div class="form-group">
        <label for="name">Name</label>
        <input id="name" type="text" v-model="name" required autocomplete="name">
      </div>

      <div class="form-group">
        <label for="email">Email</label>
        <input id="email" type="email" v-model="email" required autocomplete="email">
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input id="password" type="password" v-model="password" required autocomplete="new-password">
      </div>

      <div class="form-group">
        <label for="confirm-password">Confirm Password</label>
        <input id="confirm-password" type="password" v-model="confirmPassword" required autocomplete="new-password">
      </div>

      <div v-if="error || validationError" class="error">
        {{ error || validationError }}
      </div>

      <div><small>* indicates required field</small></div>

      <BaseButton type="submit" :disabled="loading">
        {{ loading ? 'Loading...' : 'Register' }}
      </BaseButton>

      <p class="auth-link">
        Already have an account?
        <router-link to="/login">Login here</router-link>
      </p>
    </form>
  </div>
</template>

<style scoped>
.auth-form {
  max-width: 400px;
  margin: 2rem auto;
  padding: var(--space-lg);
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);

  .auth-form-header {
    display: grid;
    margin-block-end: var(--space-md);

    * {
      margin: 0;
    }
  }
}

h2 {
  margin-bottom: var(--space-lg);
  text-align: center;
  color: var(--color-text);
}

.form-group {
  margin-bottom: var(--space-md);

  &:has(input[required]) label::after {
    content: '*';
    color: var(--color-error);
  }
}

label {
  display: block;
  margin-bottom: var(--space-xs);
  color: var(--color-text);
}

input {
  width: 100%;
  padding: var(--space-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-background);
  color: var(--color-text);
}

input:focus {
  outline: none;
  border-color: var(--color-primary);
}

button {
  width: 100%;
  padding: var(--space-sm);
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-weight: 500;
}

button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.error {
  color: var(--color-error);
  margin-bottom: var(--space-md);
  font-size: var(--font-size-sm);
}

.auth-link {
  margin-top: var(--space-md);
  text-align: center;
  font-size: var(--font-size-sm);
}

.auth-link a {
  color: var(--color-primary);
  text-decoration: none;
}

.auth-link a:hover {
  text-decoration: underline;
}

div:has(small) {
  display: block;
  text-align: center;
}
</style>
