
<script setup lang="ts">
import { ref, computed } from 'vue'
import { Camera, Save } from 'lucide-vue-next'
import { useAuth } from '@/composables/useAuth'
import BaseButton from '@/components/base/Button.vue'
import CollaboratorsList from '@/components/CollaboratorsList.vue'

const { user } = useAuth()

interface Collaborator {
  email: string
  role: 'editor' | 'viewer'
}

const username = ref(user.value?.displayName || '')
const photoURL = ref(user.value?.photoURL || '')
const collaborators = ref<Collaborator[]>([])
const isEditing = ref(false)

const fileInput = ref<HTMLInputElement | null>(null)

const handlePhotoClick = () => {
  fileInput.value?.click()
}

const handlePhotoChange = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // TODO: Implement photo upload
    const reader = new FileReader()
    reader.onload = (e) => {
      photoURL.value = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

const handleSave = async () => {
  // TODO: Implement save functionality
  isEditing.value = false
}

const handleAddCollaborator = (collaborator: Collaborator) => {
  collaborators.value.push(collaborator)
}

const handleRemoveCollaborator = (email: string) => {
  collaborators.value = collaborators.value.filter(c => c.email !== email)
}
</script>

<template>
  <div class="container">
    <div class="profile-header">
      <h1>Profile Settings</h1>
      <BaseButton @click="handleSave" :disabled="!isEditing">
        <Save class="icon" />
        Save Changes
      </BaseButton>
    </div>

    <div class="profile-content">
      <section class="profile-section card">
        <h2>Profile Information</h2>
        <div class="profile-info">
          <div class="photo-upload" @click="handlePhotoClick">
            <img
              v-if="photoURL"
              :src="photoURL"
              alt="Profile photo"
              class="profile-photo"
            >
            <div v-else class="photo-placeholder">
              <Camera class="icon" />
            </div>
            <div class="photo-overlay">
              <Camera class="icon" />
              <span>Change Photo</span>
            </div>
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              class="hidden"
              @change="handlePhotoChange"
            >
          </div>

          <div class="form-group">
            <label for="username">Username</label>
            <input
              id="username"
              v-model="username"
              type="text"
              @input="isEditing = true"
              placeholder="Enter your username"
            >
          </div>

          <div class="form-group">
            <label>Email</label>
            <p class="email">{{ user?.email }}</p>
          </div>
        </div>
      </section>

      <section class="profile-section card">
        <h2>Collaborators</h2>
        <p class="section-description">
          Add other users to collaborate on your shows. Editors can modify shows, while viewers can only view them.
        </p>
        <CollaboratorsList
          :collaborators="collaborators"
          @add="handleAddCollaborator"
          @remove="handleRemoveCollaborator"
        />
      </section>
    </div>
  </div>
</template>

<style scoped>
.container {
  padding: var(--space-lg);
  max-width: 800px;
  margin: 0 auto;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xl);
}

h1 {
  margin: 0;
  font-size: var(--font-size-2xl);
}

.profile-content {
  display: grid;
  gap: var(--space-xl);
}

.profile-section {
  h2 {
    margin: 0;
    margin-bottom: var(--space-md);
    font-size: var(--font-size-xl);
  }
}

.section-description {
  color: var(--color-text-muted);
  margin-bottom: var(--space-lg);
}

.profile-info {
  display: grid;
  gap: var(--space-lg);
  max-width: 400px;
}

.photo-upload {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  background: var(--color-surface-light);

  &:hover .photo-overlay {
    opacity: 1;
  }
}

.profile-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-muted);

  .icon {
    width: 2em;
    height: 2em;
  }
}

.photo-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity var(--transition-fast);
  gap: var(--space-xs);

  .icon {
    width: 1.5em;
    height: 1.5em;
  }

  span {
    font-size: var(--font-size-sm);
  }
}

.form-group {
  display: grid;
  gap: var(--space-xs);

  label {
    font-weight: 500;
    color: var(--color-text-muted);
  }

  input {
    padding: var(--space-sm);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-sm);
    background: var(--color-surface);
    transition: border-color var(--transition-fast);

    &:focus {
      outline: none;
      border-color: var(--color-primary);
    }
  }
}

.email {
  margin: 0;
  padding: var(--space-sm);
  background: var(--color-surface-light);
  border-radius: var(--radius-sm);
  color: var(--color-text-muted);
}

.hidden {
  display: none;
}

.icon {
  width: 1.2em;
  height: 1.2em;
}
</style>