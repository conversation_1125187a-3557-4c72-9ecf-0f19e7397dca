import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { auth } from '@/firebase'
import { onAuthStateChanged } from 'firebase/auth'
import Home from '@/views/Home.vue'
import ShowList from '@/views/shows/index.vue'
import ShowEdit from '@/views/shows/edit.vue'
import ShowView from '@/views/shows/show.vue'
import Login from '@/views/auth/Login.vue'
import Register from '@/views/auth/Register.vue'
import Loading from '@/views/Loading.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'home',
    component: Home
  },
  {
    path: '/shows',
    name: 'shows',
    component: ShowList
  },
  {
    path: '/show/new',
    name: 'new-show',
    component: ShowEdit,
  },
  {
    path: '/show/:id/edit',
    name: 'edit-show',
    component: ShowEdit,
    props: true,
  },
  {
    path: '/show/:id',
    name: 'show-details',
    component: ShowView,
    props: true,
  },
  {
    path: '/setlists',
    name: 'setlists',
    component: () => import('@/views/setlists/index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/songs',
    name: 'songs',
    component: () => import('@/views/songs/index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/song/:id/edit',
    name: 'edit-song',
    component: () => import('@/views/songs/show.vue'),
    props: true
  },
  {
    path: '/song/:id',
    name: 'song-details',
    component: () => import('@/views/songs/show.vue'),
    props: true
  },
  {
    path: '/venues',
    name: 'venues',
    component: () => import('@/views/venues/index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/user',
    name: 'user',
    component: () => import('@/views/User.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'login',
    component: Login,
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: 'register',
    component: Register,
    meta: { requiresGuest: true }
  },
  {
    path: '/loading',
    name: 'loading',
    component: Loading
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Global auth state
let isAuthReady = false
let currentUser: any = null

// Initialize auth state
onAuthStateChanged(auth, async user => {
  currentUser = user
  isAuthReady = true

  // If we're on the loading page and auth is ready, redirect to the intended destination
  if (router.currentRoute.value.path === '/loading') {
    const redirect = router.currentRoute.value.query.redirect as string
    router.replace(redirect || (currentUser ? '/' : '/login'))
  }
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest)

  // Only redirect to loading if we need to know auth state
  if (!isAuthReady && (requiresAuth || requiresGuest)) {
    return next({
      path: '/loading',
      query: { redirect: to.fullPath }
    })
  }

  // Handle auth requirements
  if (requiresAuth && !currentUser) {
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
  } else if (requiresGuest && currentUser) {
    next('/')
  } else {
    next()
  }
})

export default router
