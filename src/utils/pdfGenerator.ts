import { jsPDF } from 'jspdf'
import { ref } from 'vue'
import { format } from 'date-fns'
import type { Show, Song, StoredShow } from '@/types/'
import { callAddFont } from '@/fonts/NotoMusic-normal'
import branding from '@/assets/branding.png'
import 'svg2pdf.js'
import 'canvg'

// Initialize jsPDF with fonts
jsPDF.API?.events?.push(['addFonts', callAddFont])

/**
 * Default options for PDF generation
 */
const DEFAULT_OPTIONS = {
  marginX: 12,
  marginY: 12,
  headerHeight: 15,
  minFontSize: 8,
  maxFontSize: 100
}

const uppercase = ref(false)

/**
 * Formats a set number into a string (e.g. "ONE", "TWO", etc.)
 * @param num - The set number to format
 * @returns The formatted set number string
 */
function formatSetNumber(num: number): string {
  return ['ONE', 'TWO', 'THREE', 'FOUR'][num] || String(num + 1)
}

/**
 * Formats a date string into a string (e.g. "12 JAN 22")
 * @param date - The date string to format
 * @returns The formatted date string
 */
function formatDate(date: string): string {
  return format(new Date(date), 'd MMM yy').toUpperCase()
}

function title(song: Song): string {
  return uppercase.value ? song.title.toUpperCase() : song.title
}

/**
 * Adds a header to the PDF document
 * @param doc - The jsPDF document instance
 * @param show - The show data to display in the header
 * @param setIndex - The index of the current set
 */
function addHeader(doc: jsPDF, show: StoredShow | Show, setIndex: number) {
  const pageWidth = doc.internal.pageSize.getWidth()
  const maxAllowedWidth = pageWidth - 2 * DEFAULT_OPTIONS.marginX
  const set = show.sets[setIndex]
  const headerText = `${show.act} - ${show.venue} - ${formatDate(
    show.date
  )} - SET ${formatSetNumber(setIndex)}${
    set.title ? ': ' + set.title : ''
  }`.toUpperCase()

  let fontSize = 18

  do {
    doc.setFontSize(fontSize)
    const textWidth =
      (doc.getStringUnitWidth(headerText) * fontSize) / doc.internal.scaleFactor
    if (textWidth <= maxAllowedWidth || fontSize <= 10) break
    fontSize--
  } while (fontSize > 10)

  doc.text(headerText, pageWidth / 2, DEFAULT_OPTIONS.marginY, {
    align: 'center'
  })
}

/**
 * Finds the optimal font size and spacing for the given songs
 * @param doc - The jsPDF document instance
 * @param songs - Array of song strings to measure
 * @returns Object containing the optimal fontSize and spacing
 */
function findOptimalFontSize(doc: jsPDF, songs: string[]) {
  const pageWidth = doc.internal.pageSize.getWidth()
  const pageHeight = doc.internal.pageSize.getHeight()
  const maxAllowedWidth = pageWidth - 2 * DEFAULT_OPTIONS.marginX
  let optimalSize = DEFAULT_OPTIONS.maxFontSize

  // First pass: find optimal size based on width
  songs.forEach(song => {
    let currentSize = 12
    doc.setFontSize(currentSize)
    let textWidth =
      (doc.getStringUnitWidth(song) * currentSize) / doc.internal.scaleFactor

    // Increase font size until it reaches the maximum allowed width
    // or the maximum allowed font size
    while (
      textWidth < maxAllowedWidth &&
      currentSize < DEFAULT_OPTIONS.maxFontSize
    ) {
      currentSize++
      doc.setFontSize(currentSize)
      textWidth =
        (doc.getStringUnitWidth(song) * currentSize) / doc.internal.scaleFactor
    }

    // Reduce font size until the text fits within the maximum allowed width
    while (
      textWidth > maxAllowedWidth &&
      currentSize > DEFAULT_OPTIONS.minFontSize
    ) {
      currentSize--
      doc.setFontSize(currentSize)
      textWidth =
        (doc.getStringUnitWidth(song) * currentSize) / doc.internal.scaleFactor
    }

    optimalSize = Math.min(optimalSize, currentSize)
  })

  // Second pass: reduce size if songs don't fit on page
  let fontSize = optimalSize
  let spacing: number

  do {
    spacing = 0.4
    const totalHeight =
      songs.length * fontSize * spacing + DEFAULT_OPTIONS.headerHeight + 10
    if (totalHeight > pageHeight - 2 * DEFAULT_OPTIONS.marginY) {
      fontSize--
    } else {
      break
    }
  } while (fontSize >= DEFAULT_OPTIONS.minFontSize)

  fontSize = Math.max(fontSize, DEFAULT_OPTIONS.minFontSize)
  return { fontSize, spacing }
}

/**
 * Adds a branding logo to the footer of the PDF
 * @param doc - The jsPDF document instance
 * @param brandingImg - Optional custom branding image
 */
function addFooterLogo(doc: jsPDF, brandingImg = branding) {
  try {
    const pageWidth = doc.internal.pageSize.getWidth()
    const pageHeight = doc.internal.pageSize.getHeight()

    // Calculate dimensions and position
    const imgWidth = 40
    const imgHeight = 10
    const x = pageWidth / 2 - imgWidth / 2
    const y = pageHeight - DEFAULT_OPTIONS.marginY - imgHeight

    // Add the image using the correct format
    doc.addImage(brandingImg, 'PNG', 0, 0, 40, 10)
  } catch (error) {
    console.error('Error adding footer logo:', error)
  }
}

/**
 * Generates a PDF setlist document for a show
 * @param show - The show data to generate the PDF from
 * @param brandingImg - Optional custom branding image
 * @returns A jsPDF document instance
 */
export function generateSetListPDF(
  show: StoredShow | Show,
  toUppercase: boolean = true
): jsPDF {
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4'
  })

  uppercase.value = toUppercase

  doc.setFont('NotoMusic')

  for (let setIndex = 0; setIndex < show.sets.length; setIndex++) {
    if (setIndex > 0) {
      doc.addPage()
    }

    addHeader(doc, show, setIndex)

    // Split songs into main set and encore
    const lastSongIndex = show.sets[setIndex].songs.findIndex(
      song => song.isLastSong
    )
    const mainSetSongs =
      lastSongIndex !== -1
        ? show.sets[setIndex].songs.slice(0, lastSongIndex + 1)
        : show.sets[setIndex].songs
    const encoreSongs =
      lastSongIndex !== -1
        ? show.sets[setIndex].songs.slice(lastSongIndex + 1)
        : []

    // Format all songs for sizing calculation
    const songTitles = [
      ...mainSetSongs.map(song => `${title(song)}  ${song.key}`),
      ...(encoreSongs.length > 0 ? [''] : []),
      ...encoreSongs.map(song => `${title(song)} ${song.key}`)
    ]

    const { fontSize, spacing } = findOptimalFontSize(doc, songTitles)
    let yOffset = DEFAULT_OPTIONS.marginY + DEFAULT_OPTIONS.headerHeight + 10

    doc.setFontSize(fontSize)

    // Render main set
    mainSetSongs.forEach(song => {
      renderSong(doc, song, yOffset, fontSize)
      yOffset += Math.min(fontSize * spacing, fontSize)
    })

    // Add encore section if exists
    if (encoreSongs.length > 0) {
      // Only mark encores if this is the last set
      if (setIndex + 1 === show.sets.length) {
        yOffset -= (fontSize * spacing) / 2
        doc.setLineWidth(0.5)
        doc.line(
          DEFAULT_OPTIONS.marginX,
          yOffset,
          doc.internal.pageSize.getWidth() - DEFAULT_OPTIONS.marginX,
          yOffset
        )

        yOffset += fontSize * spacing
      }

      encoreSongs.forEach(song => {
        renderSong(doc, song, yOffset, fontSize)
        yOffset += Math.min(fontSize * spacing, fontSize)
      })
    }

    // TODO: fix this
    // addFooterLogo(doc, brandingImg)
  }

  return doc
}

/**
 * Renders a single song in the PDF document
 * @param doc - The jsPDF document instance
 * @param song - The song data to render
 * @param yOffset - The vertical position to render at
 * @param fontSize - The font size to use
 */
function renderSong(doc: jsPDF, song: Song, yOffset: number, fontSize: number) {
  doc.text(title(song), DEFAULT_OPTIONS.marginX, yOffset)

  const titleWidth =
    (doc.getStringUnitWidth(title(song)) * fontSize) / doc.internal.scaleFactor

  // Split the key into parts and render each with appropriate y-offset
  const keyParts = song.key.match(/^([A-G])([♭♯])?([mM])?$/)
  if (keyParts) {
    const [_, note, accidental, quality] = keyParts
    const xStart =
      DEFAULT_OPTIONS.marginX +
      titleWidth +
      (doc.getStringUnitWidth('  ') * fontSize) / doc.internal.scaleFactor

    // Render note
    doc.text(note, xStart, yOffset)

    // Render accidental (if present) with adjusted y-position
    if (accidental) {
      const accidentalWidth =
        (doc.getStringUnitWidth(note) * fontSize) / doc.internal.scaleFactor
      const accidentalYOffset = yOffset - fontSize * 0.125
      doc.text(accidental, xStart + accidentalWidth, accidentalYOffset)
    }

    // Render quality (if present)
    if (quality) {
      const noteAccidentalWidth =
        (doc.getStringUnitWidth(note + (accidental || '')) * fontSize) /
        doc.internal.scaleFactor
      doc.text(quality, xStart + noteAccidentalWidth, yOffset)
    }
  } else {
    // Fallback for any keys that don't match the expected format
    doc.text(`  ${song.key}`, DEFAULT_OPTIONS.marginX + titleWidth, yOffset)
  }
}
