/**
 * Import function triggers from their respective submodules:
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

import * as functions from 'firebase-functions/v1'
import * as admin from 'firebase-admin'
import { UserRecord } from 'firebase-admin/auth'

admin.initializeApp()

export const createUser = functions.auth.user().onCreate((user: UserRecord) => {
  const userDoc = {
    uid: user.uid,
    email: user.email,
    roles: [],
    createdAt: admin.firestore.FieldValue.serverTimestamp()
  }

  return admin.firestore().collection('users').doc(user.uid).set(userDoc)
})

export const deleteUser = functions.auth.user().onDelete((user: UserRecord) => {
  return admin.firestore().collection('users').doc(user.uid).delete()
})
